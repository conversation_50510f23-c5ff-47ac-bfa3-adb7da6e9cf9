import { logger } from '../../../utils/logger'

/**
 * Advanced Memory Optimizer
 * Addresses high memory usage and prevents memory explosions
 */
export class AdvancedMemoryOptimizer {
  private optimizationInterval: NodeJS.Timeout | null = null
  private memoryMonitoringInterval: NodeJS.Timeout | null = null
  private initialized: boolean = false
  private lastOptimizationTime: number = 0
  private memoryStats: any = {}

  private config = {
    optimizationIntervalMs: 60000, // 1 minute (more frequent)
    monitoringIntervalMs: 15000, // 15 seconds (more frequent)
    criticalMemoryThreshold: 0.60, // 60% (much lower threshold)
    warningMemoryThreshold: 0.45, // 45% (much lower threshold)
    maxMemoryNodesPerSystem: 200, // Much more aggressive
    maxBlackboardEntriesPerTopic: 25, // Much more aggressive
    enableAggressiveCleanup: true,
    enableMemoryCompaction: true,
    enableGarbageCollection: true,
    maxMemoryUsageGB: 2 // 2GB limit (much more aggressive)
  }

  constructor(
    private blackboard: any,
    private memoryForest: any,
    private spacetimeDB: any
  ) {}

  async initialize(): Promise<void> {
    if (this.initialized) return

    logger.info('🚀 Initializing Advanced Memory Optimizer...')

    // Start memory monitoring
    this.startMemoryMonitoring()

    // Start optimization cycles
    this.startOptimizationCycles()

    // Hook into system memory events
    this.hookMemoryEvents()

    this.initialized = true
    logger.info('✅ Advanced Memory Optimizer initialized successfully!')
  }

  private startMemoryMonitoring(): void {
    this.memoryMonitoringInterval = setInterval(() => {
      this.monitorMemoryUsage()
    }, this.config.monitoringIntervalMs)

    logger.info('📊 Memory monitoring started')
  }

  private startOptimizationCycles(): void {
    this.optimizationInterval = setInterval(() => {
      this.performMemoryOptimization()
    }, this.config.optimizationIntervalMs)

    logger.info('🔧 Memory optimization cycles started')
  }

  private monitorMemoryUsage(): void {
    try {
      const memoryUsage = process.memoryUsage()
      const memoryUsageGB = memoryUsage.heapUsed / (1024 * 1024 * 1024)
      const memoryPercentage = memoryUsageGB / this.config.maxMemoryUsageGB

      this.memoryStats = {
        heapUsed: memoryUsage.heapUsed,
        heapTotal: memoryUsage.heapTotal,
        external: memoryUsage.external,
        rss: memoryUsage.rss,
        memoryUsageGB: memoryUsageGB,
        memoryPercentage: memoryPercentage,
        timestamp: Date.now()
      }

      // Check for critical memory usage
      if (memoryPercentage > this.config.criticalMemoryThreshold) {
        logger.warn(`🚨 CRITICAL MEMORY USAGE: ${(memoryPercentage * 100).toFixed(1)}% (${memoryUsageGB.toFixed(2)}GB)`)
        this.performEmergencyMemoryCleanup()
      } else if (memoryPercentage > this.config.warningMemoryThreshold) {
        logger.warn(`⚠️ HIGH MEMORY USAGE: ${(memoryPercentage * 100).toFixed(1)}% (${memoryUsageGB.toFixed(2)}GB)`)
        this.performMemoryOptimization()
      }

      // Log memory stats periodically
      if (Date.now() - this.lastOptimizationTime > 300000) { // Every 5 minutes
        logger.info(`📊 Memory: ${memoryUsageGB.toFixed(2)}GB (${(memoryPercentage * 100).toFixed(1)}%)`)
      }

    } catch (error) {
      logger.error('❌ Memory monitoring failed:', error)
    }
  }

  private async performMemoryOptimization(): Promise<void> {
    try {
      logger.info('🔧 Starting memory optimization...')

      const startTime = Date.now()
      let totalFreed = 0

      // 1. Optimize MemoryForest
      const memoryForestFreed = await this.optimizeMemoryForest()
      totalFreed += memoryForestFreed

      // 2. Optimize Blackboard
      const blackboardFreed = await this.optimizeBlackboard()
      totalFreed += blackboardFreed

      // 3. Optimize SpacetimeDB
      const spacetimeFreed = await this.optimizeSpacetimeDB()
      totalFreed += spacetimeFreed

      // 4. Perform garbage collection
      if (this.config.enableGarbageCollection && global.gc) {
        global.gc()
        logger.info('🗑️ Forced garbage collection')
      }

      // 5. Compact memory if enabled
      if (this.config.enableMemoryCompaction) {
        await this.performMemoryCompaction()
      }

      const duration = Date.now() - startTime
      this.lastOptimizationTime = Date.now()

      logger.info(`✅ Memory optimization completed: ${(totalFreed / (1024 * 1024)).toFixed(1)}MB freed in ${duration}ms`)

      // Publish optimization results
      if (this.blackboard && this.blackboard.publish) {
        this.blackboard.publish('memory_optimization_completed', {
          totalFreed: totalFreed,
          duration: duration,
          timestamp: Date.now()
        })
      }

    } catch (error) {
      logger.error('❌ Memory optimization failed:', error)
    }
  }

  private async optimizeMemoryForest(): Promise<number> {
    let freedBytes = 0

    try {
      if (!this.memoryForest) return 0

      // Count current nodes
      let nodeCount = 0
      if (this.memoryForest.nodes) {
        nodeCount = this.memoryForest.nodes.size
      } else if (this.memoryForest.getAllNodes) {
        nodeCount = this.memoryForest.getAllNodes().length
      }

      if (nodeCount > this.config.maxMemoryNodesPerSystem) {
        logger.info(`🌲 MemoryForest optimization: ${nodeCount} nodes (limit: ${this.config.maxMemoryNodesPerSystem})`)

        // Remove oldest nodes if we have too many
        const nodesToRemove = nodeCount - this.config.maxMemoryNodesPerSystem
        
        if (this.memoryForest.removeOldestNodes) {
          freedBytes = await this.memoryForest.removeOldestNodes(nodesToRemove)
        } else if (this.memoryForest.nodes) {
          // Manual cleanup if method doesn't exist
          const nodes = Array.from(this.memoryForest.nodes.entries()) as [string, any][]
          nodes.sort((a: [string, any], b: [string, any]) => ((a[1].timestamp || 0) - (b[1].timestamp || 0)))

          for (let i = 0; i < nodesToRemove && i < nodes.length; i++) {
            const [nodeId, node] = nodes[i] as [string, any]
            const nodeSize = JSON.stringify(node).length
            this.memoryForest.nodes.delete(nodeId)
            freedBytes += nodeSize
          }
        }

        logger.info(`🌲 MemoryForest cleaned: ${nodesToRemove} nodes removed, ${(freedBytes / (1024 * 1024)).toFixed(1)}MB freed`)
      }

    } catch (error) {
      logger.error('❌ MemoryForest optimization failed:', error)
    }

    return freedBytes
  }

  private async optimizeBlackboard(): Promise<number> {
    let freedBytes = 0

    try {
      if (!this.blackboard) return 0

      // Clean up old blackboard entries
      if (this.blackboard.cleanupOldEntries) {
        freedBytes = await this.blackboard.cleanupOldEntries(this.config.maxBlackboardEntriesPerTopic)
      } else if (this.blackboard.data) {
        // Manual cleanup if method doesn't exist
        for (const [topic, entries] of this.blackboard.data) {
          if (Array.isArray(entries) && entries.length > this.config.maxBlackboardEntriesPerTopic) {
            const entriesToRemove = entries.length - this.config.maxBlackboardEntriesPerTopic
            const removedEntries = entries.splice(0, entriesToRemove)
            freedBytes += JSON.stringify(removedEntries).length
          }
        }
      }

      if (freedBytes > 0) {
        logger.info(`📋 Blackboard cleaned: ${(freedBytes / (1024 * 1024)).toFixed(1)}MB freed`)
      }

    } catch (error) {
      logger.error('❌ Blackboard optimization failed:', error)
    }

    return freedBytes
  }

  private async optimizeSpacetimeDB(): Promise<number> {
    let freedBytes = 0

    try {
      if (!this.spacetimeDB) return 0

      // Clean up old spacetime events
      if (this.spacetimeDB.cleanupOldEvents) {
        freedBytes = await this.spacetimeDB.cleanupOldEvents(1000) // Keep last 1000 events
      }

      if (freedBytes > 0) {
        logger.info(`🌌 SpacetimeDB cleaned: ${(freedBytes / (1024 * 1024)).toFixed(1)}MB freed`)
      }

    } catch (error) {
      logger.error('❌ SpacetimeDB optimization failed:', error)
    }

    return freedBytes
  }

  private async performMemoryCompaction(): Promise<void> {
    try {
      // Compact memory by recreating data structures
      if (this.memoryForest && this.memoryForest.compact) {
        await this.memoryForest.compact()
      }

      if (this.blackboard && this.blackboard.compact) {
        await this.blackboard.compact()
      }

      logger.info('🗜️ Memory compaction completed')

    } catch (error) {
      logger.error('❌ Memory compaction failed:', error)
    }
  }

  private async performEmergencyMemoryCleanup(): Promise<void> {
    try {
      logger.warn('🚨 Performing emergency memory cleanup...')

      // Aggressive cleanup with lower thresholds
      const originalMaxNodes = this.config.maxMemoryNodesPerSystem
      const originalMaxEntries = this.config.maxBlackboardEntriesPerTopic

      // Temporarily reduce limits for emergency cleanup
      this.config.maxMemoryNodesPerSystem = Math.floor(originalMaxNodes * 0.5)
      this.config.maxBlackboardEntriesPerTopic = Math.floor(originalMaxEntries * 0.5)

      // Perform optimization with reduced limits
      await this.performMemoryOptimization()

      // Force multiple garbage collections
      if (global.gc) {
        for (let i = 0; i < 3; i++) {
          global.gc()
          await new Promise(resolve => setTimeout(resolve, 100))
        }
      }

      // Restore original limits
      this.config.maxMemoryNodesPerSystem = originalMaxNodes
      this.config.maxBlackboardEntriesPerTopic = originalMaxEntries

      logger.warn('⚠️ Emergency memory cleanup completed')

    } catch (error) {
      logger.error('❌ Emergency memory cleanup failed:', error)
    }
  }

  private hookMemoryEvents(): void {
    // Hook into memory-related events from other systems
    if (this.blackboard && this.blackboard.subscribe) {
      this.blackboard.subscribe('memory_leak_detected', (data: any) => {
        logger.warn('🚨 Memory leak detected by another system, triggering optimization')
        this.performMemoryOptimization()
      })

      this.blackboard.subscribe('high_memory_usage', (data: any) => {
        logger.warn('⚠️ High memory usage reported, triggering optimization')
        this.performMemoryOptimization()
      })
    }
  }

  getMemoryStats(): any {
    return {
      ...this.memoryStats,
      lastOptimizationTime: this.lastOptimizationTime,
      config: this.config,
      optimizationActive: !!this.optimizationInterval,
      monitoringActive: !!this.memoryMonitoringInterval
    }
  }

  async shutdown(): Promise<void> {
    if (this.optimizationInterval) {
      clearInterval(this.optimizationInterval)
    }
    if (this.memoryMonitoringInterval) {
      clearInterval(this.memoryMonitoringInterval)
    }

    // Final optimization before shutdown
    await this.performMemoryOptimization()

    logger.info('🛑 Advanced Memory Optimizer shutdown completed')
  }
}
