import { logger } from '../../../utils/logger'
import { IntelligentPerpetualMemory } from './intelligent-perpetual-memory'
import { MemoryCleanupUtility, runEmergencyMemoryCleanup } from './memory-cleanup-utility'

/**
 * Memory System Integration
 * 
 * Replaces all the bloated memory systems with the new Intelligent Perpetual Memory
 * and integrates it with Alice's existing systems.
 */
export class MemorySystemIntegration {
  private intelligentMemory: IntelligentPerpetualMemory | null = null
  private initialized: boolean = false

  constructor(
    private blackboard: any,
    private memoryForest?: any,
    private spacetimeDB?: any
  ) {}

  async initialize(): Promise<void> {
    if (this.initialized) return

    logger.info('🔄 Initializing Memory System Integration...')

    try {
      // Step 1: Emergency cleanup of bloated systems
      logger.info('🚨 Step 1: Emergency cleanup of bloated memory systems...')
      await runEmergencyMemoryCleanup()

      // Step 2: Initialize new intelligent memory system
      logger.info('🧠 Step 2: Initializing Intelligent Perpetual Memory...')
      this.intelligentMemory = new IntelligentPerpetualMemory(
        this.blackboard,
        this.memoryForest,
        this.spacetimeDB
      )
      await this.intelligentMemory.initialize()

      // Step 3: Integrate with existing systems
      logger.info('🔗 Step 3: Integrating with existing Alice systems...')
      await this.integrateWithExistingSystems()

      // Step 4: Disable problematic memory systems
      logger.info('🚫 Step 4: Disabling problematic memory systems...')
      await this.disableProblematicSystems()

      // Step 5: Set up monitoring
      logger.info('📊 Step 5: Setting up memory monitoring...')
      await this.setupMemoryMonitoring()

      this.initialized = true
      logger.info('✅ Memory System Integration completed successfully!')

      // Report final status
      const stats = await this.getMemorySystemStatus()
      logger.info('📊 Final Memory System Status:')
      logger.info(`   - Total memories: ${stats.totalMemories}`)
      logger.info(`   - Storage used: ${(stats.storageSize / 1024 / 1024).toFixed(1)}MB`)
      logger.info(`   - Storage limit: ${(stats.maxStorageSize / 1024 / 1024).toFixed(1)}MB`)
      logger.info(`   - Usage: ${stats.storageUsagePercent.toFixed(1)}%`)

    } catch (error) {
      logger.error('❌ Memory System Integration failed:', error)
      throw error
    }
  }

  private async integrateWithExistingSystems(): Promise<void> {
    if (!this.intelligentMemory) return

    // Integrate with blackboard
    await this.intelligentMemory.integrateWithBlackboard(this.blackboard)

    // Integrate with memory forest if available
    if (this.memoryForest) {
      await this.intelligentMemory.integrateWithMemoryForest(this.memoryForest)
    }

    // Register with blackboard as the primary memory system
    this.blackboard.writeToBlackboard('alice-systems/memory-system', {
      system: 'IntelligentPerpetualMemory',
      status: 'active',
      timestamp: Date.now(),
      capabilities: [
        'intelligent-compression',
        'deduplication',
        'temporal-layering',
        'importance-scoring',
        'automatic-cleanup',
        'memory-search',
        'memory-export'
      ]
    })

    logger.info('🔗 Integrated with existing Alice systems')
  }

  private async disableProblematicSystems(): Promise<void> {
    // Disable RecursiveSelfImprovement system that was creating thousands of directories
    this.blackboard.writeToBlackboard('alice-systems/recursive-self-improvement', {
      status: 'disabled',
      reason: 'Creating excessive experiment directories',
      timestamp: Date.now(),
      disabledBy: 'MemorySystemIntegration'
    })

    // Disable memory snapshot systems that were creating hundreds of files
    this.blackboard.writeToBlackboard('alice-systems/memory-snapshots', {
      status: 'disabled',
      reason: 'Creating excessive snapshot files',
      timestamp: Date.now(),
      disabledBy: 'MemorySystemIntegration'
    })

    // Disable other memory systems to prevent conflicts
    const systemsToDisable = [
      'ultimate-perpetual-memory-system',
      'memory-immortality-manager',
      'memory-optimization-systems',
      'perpetual-blackboard-memory',
      'immortal-memory-system'
    ]

    for (const system of systemsToDisable) {
      this.blackboard.writeToBlackboard(`alice-systems/${system}`, {
        status: 'disabled',
        reason: 'Replaced by IntelligentPerpetualMemory',
        timestamp: Date.now(),
        disabledBy: 'MemorySystemIntegration'
      })
    }

    logger.info('🚫 Disabled problematic memory systems')
  }

  private async setupMemoryMonitoring(): Promise<void> {
    if (!this.intelligentMemory) return

    // Set up periodic monitoring
    setInterval(async () => {
      try {
        const stats = await this.intelligentMemory!.getMemoryStats()
        
        // Log stats to blackboard
        this.blackboard.writeToBlackboard('alice-monitoring/memory-stats', {
          timestamp: Date.now(),
          ...stats
        })

        // Check for issues
        if (stats.storageUsagePercent > 80) {
          logger.warn(`⚠️ Memory usage high: ${stats.storageUsagePercent.toFixed(1)}%`)
          this.blackboard.writeToBlackboard('alice-alerts/memory-usage-high', {
            timestamp: Date.now(),
            usage: stats.storageUsagePercent,
            totalSize: stats.storageSize,
            maxSize: stats.maxStorageSize
          })
        }

        if (stats.totalMemories > 10000) {
          logger.warn(`⚠️ High memory count: ${stats.totalMemories}`)
          this.blackboard.writeToBlackboard('alice-alerts/memory-count-high', {
            timestamp: Date.now(),
            count: stats.totalMemories
          })
        }

      } catch (error) {
        logger.error('❌ Memory monitoring error:', error)
      }
    }, 5 * 60 * 1000) // Every 5 minutes

    logger.info('📊 Memory monitoring set up')
  }

  async storeMemory(type: string, content: any, importance: number = 0.5, metadata: any = {}): Promise<string | null> {
    if (!this.intelligentMemory) {
      logger.error('❌ Intelligent memory system not initialized')
      return null
    }

    try {
      return await this.intelligentMemory.storeMemory(type as any, content, importance, metadata)
    } catch (error) {
      logger.error('❌ Failed to store memory:', error)
      return null
    }
  }

  async retrieveMemory(memoryId: string): Promise<any | null> {
    if (!this.intelligentMemory) {
      logger.error('❌ Intelligent memory system not initialized')
      return null
    }

    try {
      return await this.intelligentMemory.retrieveMemory(memoryId)
    } catch (error) {
      logger.error('❌ Failed to retrieve memory:', error)
      return null
    }
  }

  async searchMemories(query: string, type?: string, limit: number = 10): Promise<any[]> {
    if (!this.intelligentMemory) {
      logger.error('❌ Intelligent memory system not initialized')
      return []
    }

    try {
      return await this.intelligentMemory.searchMemories(query, type as any, limit)
    } catch (error) {
      logger.error('❌ Failed to search memories:', error)
      return []
    }
  }

  async getMemorySystemStatus(): Promise<any> {
    if (!this.intelligentMemory) {
      return {
        initialized: false,
        error: 'Memory system not initialized'
      }
    }

    try {
      return await this.intelligentMemory.getMemoryStats()
    } catch (error) {
      logger.error('❌ Failed to get memory system status:', error)
      return {
        initialized: false,
        error: error instanceof Error ? error.message : String(error)
      }
    }
  }

  async exportMemories(options: { type?: string, minImportance?: number } = {}): Promise<any[]> {
    if (!this.intelligentMemory) {
      logger.error('❌ Intelligent memory system not initialized')
      return []
    }

    try {
      return await this.intelligentMemory.exportMemories(options as any)
    } catch (error) {
      logger.error('❌ Failed to export memories:', error)
      return []
    }
  }

  async performManualCleanup(): Promise<void> {
    logger.info('🧹 Performing manual memory cleanup...')
    
    try {
      // Run emergency cleanup again
      await runEmergencyMemoryCleanup()
      
      // Force cleanup in intelligent memory system
      if (this.intelligentMemory) {
        // Access private method through any cast (for emergency cleanup)
        await (this.intelligentMemory as any).performCleanup()
      }
      
      logger.info('✅ Manual cleanup completed')
    } catch (error) {
      logger.error('❌ Manual cleanup failed:', error)
    }
  }

  async shutdown(): Promise<void> {
    logger.info('🛑 Shutting down Memory System Integration...')

    if (this.intelligentMemory) {
      await this.intelligentMemory.shutdown()
    }

    this.initialized = false
    logger.info('✅ Memory System Integration shutdown completed')
  }

  // Convenience methods for Alice's existing code
  async storeConversationMemory(userMessage: string, aliceResponse: string, importance: number = 0.6): Promise<string | null> {
    return await this.storeMemory('conversation', {
      userMessage,
      aliceResponse,
      timestamp: Date.now()
    }, importance, {
      type: 'conversation',
      participants: ['user', 'alice']
    })
  }

  async storeLearningMemory(topic: string, content: any, importance: number = 0.7): Promise<string | null> {
    return await this.storeMemory('learning', {
      topic,
      content,
      timestamp: Date.now()
    }, importance, {
      type: 'learning',
      category: topic
    })
  }

  async storeSystemMemory(event: string, data: any, importance: number = 0.5): Promise<string | null> {
    return await this.storeMemory('system', {
      event,
      data,
      timestamp: Date.now()
    }, importance, {
      type: 'system',
      event
    })
  }

  async searchConversations(query: string, limit: number = 10): Promise<any[]> {
    return await this.searchMemories(query, 'conversation', limit)
  }

  async searchLearnings(query: string, limit: number = 10): Promise<any[]> {
    return await this.searchMemories(query, 'learning', limit)
  }
}
