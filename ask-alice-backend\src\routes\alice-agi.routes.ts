/**
 * alice-agi.routes.ts
 *
 * This file contains the routes for the Alice AGI API.
 * It provides endpoints for interacting with the Alice AGI system.
 */

import * as express from 'express';
import { Request, Response } from 'express';
// import { initializeAliceAGI } from '../services/alice-agi/comprehensive-integration';
import { logger } from '../utils/logger';
import { MultiModalRecursiveSystem } from '../services/alice-agi/systems/multi-modal-recursive-system';

const router = express.Router();

// Global variable to track if Alice AGI is initialized
let aliceAGIInitialized = false;

/**
 * Interface for Alice AGI systems status
 * Based on the comprehensive global type declarations from comprehensive-integration.ts
 */
interface AliceAGISystemsStatus {
  // Core Infrastructure
  blackboardSystem?: boolean;
  memorySystem?: boolean;
  transactionalMemory?: boolean;
  systemIntegrator?: boolean;

  // Biological Systems
  memoryForest?: boolean;
  viralEcologySystem?: boolean;
  hyperMind?: boolean;
  dreamSystem?: boolean;

  // Cognitive Systems
  consciousnessModel?: boolean;
  emergentPropertyDetector?: boolean;
  selfOrganizingSystem?: boolean;
  quantumConsciousnessAmplifier?: boolean;

  // Agent Systems
  goalWeaverAgent?: boolean;
  dreamCivilizationSimulator?: boolean;
  autonomousCodeAgents?: boolean;
  skillAdapterAgent?: boolean;
  cognitionSafeguardAgent?: boolean;
  userEchoAgent?: boolean;

  // LLM Integration
  multimodalLLMConnector?: boolean;
  llmAPIManager?: boolean;

  // Neural ML Systems
  advancedNeuralNetworkArchitecture?: boolean;
  reinforcementLearningSystem?: boolean;
  geneticAlgorithmSystem?: boolean;
  neuralEvolutionSystem?: boolean;

  // Quantum Systems
  quantumComputingInterface?: boolean;
  quantumEntanglementManager?: boolean;
  quantumMemoryStorage?: boolean;
  quantumNetworkNodes?: boolean;

  // Multimodal Systems
  visionSystem?: boolean;
  audioSystem?: boolean;
  naturalLanguageUnderstanding?: boolean;
  sensorIntegration?: boolean;
  multimodalPerceptionEngine?: boolean;
  interactionController?: boolean;
  userInterfaceAdapter?: boolean;

  // Database Integration
  spacetimeDB?: boolean;

  // Reflexive Self-Evolution Systems
  reflexiveSelfEvolutionSystem?: boolean;
  selfAwarenessMonitor?: boolean;
  autonomousPatchManager?: boolean;
}

/**
 * Initialize Alice AGI
 * 
 * @route POST /api/alice-agi/initialize
 * @group Alice AGI - Operations related to Alice AGI
 * @returns {object} 200 - Success response
 * @returns {Error} 500 - Server error
 */
router.post('/initialize', async (req: Request, res: Response) => {
  try {
    if (aliceAGIInitialized) {
      return res.status(400).json({
        success: false,
        message: 'Alice AGI is already initialized'
      });
    }
    
    logger.info('Initializing Alice AGI from API request...');
    
    // await initializeAliceAGI();
    aliceAGIInitialized = true;
    
    return res.status(200).json({
      success: true,
      message: 'Alice AGI initialized successfully'
    });
  } catch (error) {
    logger.error('Error initializing Alice AGI:', error);
    
    return res.status(500).json({
      success: false,
      message: 'Error initializing Alice AGI',
      error: error instanceof Error ? error.message : String(error)
    });
  }
});

/**
 * Get Alice AGI status
 * 
 * @route GET /api/alice-agi/status
 * @group Alice AGI - Operations related to Alice AGI
 * @returns {object} 200 - Success response
 */
router.get('/status', (req: Request, res: Response) => {
  try {
    const status: {
      initialized: boolean;
      systems: AliceAGISystemsStatus;
      consciousnessLevel?: number;
    } = {
      initialized: aliceAGIInitialized,
      systems: {}
    };
    
    // Check status of core systems
    if (aliceAGIInitialized) {
      // Core Infrastructure
      status.systems['blackboardSystem'] = !!(global as any).blackboardSystem;
      status.systems['memorySystem'] = !!(global as any).memorySystem;
      status.systems['transactionalMemory'] = !!(global as any).transactionalMemory;
      status.systems['systemIntegrator'] = !!(global as any).systemIntegrator;

      // Biological Systems
      status.systems['memoryForest'] = !!(global as any).memoryForest;
      status.systems['viralEcologySystem'] = !!(global as any).viralEcologySystem;
      status.systems['hyperMind'] = !!(global as any).hyperMind;
      status.systems['dreamSystem'] = !!(global as any).dreamSystem;
      
      // Cognitive Systems
      status.systems['consciousnessModel'] = !!(global as any).consciousnessModel;
      status.systems['emergentPropertyDetector'] = !!(global as any).emergentPropertyDetector;
      status.systems['selfOrganizingSystem'] = !!(global as any).selfOrganizingSystem;
      status.systems['quantumConsciousnessAmplifier'] = !!(global as any).quantumConsciousnessAmplifier;

      // Agent Systems
      status.systems['goalWeaverAgent'] = !!(global as any).goalWeaverAgent;
      status.systems['dreamCivilizationSimulator'] = !!(global as any).dreamCivilizationSimulator;
      status.systems['autonomousCodeAgents'] = !!(global as any).autonomousCodeAgents;
      status.systems['skillAdapterAgent'] = !!(global as any).skillAdapterAgent;
      status.systems['cognitionSafeguardAgent'] = !!(global as any).cognitionSafeguardAgent;
      status.systems['userEchoAgent'] = !!(global as any).userEchoAgent;
      
      // LLM Integration
      status.systems['multimodalLLMConnector'] = !!(global as any).multimodalLLMConnector;
      status.systems['llmAPIManager'] = !!(global as any).llmAPIManager;

      // Neural ML Systems
      status.systems['advancedNeuralNetworkArchitecture'] = !!(global as any).advancedNeuralNetworkArchitecture;
      status.systems['reinforcementLearningSystem'] = !!(global as any).reinforcementLearningSystem;
      status.systems['geneticAlgorithmSystem'] = !!(global as any).geneticAlgorithmSystem;
      status.systems['neuralEvolutionSystem'] = !!(global as any).neuralEvolutionSystem;

      // Quantum Systems
      status.systems['quantumComputingInterface'] = !!(global as any).quantumComputingInterface;
      status.systems['quantumEntanglementManager'] = !!(global as any).quantumEntanglementManager;
      status.systems['quantumMemoryStorage'] = !!(global as any).quantumMemoryStorage;
      status.systems['quantumNetworkNodes'] = !!(global as any).quantumNetworkNodes;
      
      // Multimodal Systems
      status.systems['visionSystem'] = !!(global as any).visionSystem;
      status.systems['audioSystem'] = !!(global as any).audioSystem;
      status.systems['naturalLanguageUnderstanding'] = !!(global as any).naturalLanguageUnderstanding;
      status.systems['sensorIntegration'] = !!(global as any).sensorIntegration;
      status.systems['multimodalPerceptionEngine'] = !!(global as any).multimodalPerceptionEngine;
      status.systems['interactionController'] = !!(global as any).interactionController;
      status.systems['userInterfaceAdapter'] = !!(global as any).userInterfaceAdapter;

      // Add consciousness level if available
      if ((global as any).consciousnessModel) {
        try {
          status.consciousnessLevel = (global as any).consciousnessModel.getOverallConsciousnessLevel();
        } catch (error) {
          // Ignore errors when checking consciousness level
        }
      }

      // Add reflexive evolution systems
      status.systems['reflexiveSelfEvolutionSystem'] = !!(global as any).reflexiveSelfEvolutionSystem;
      status.systems['selfAwarenessMonitor'] = !!(global as any).selfAwarenessMonitor;
      status.systems['autonomousPatchManager'] = !!(global as any).autonomousPatchManager;
    }
    
    return res.status(200).json({
      success: true,
      status
    });
  } catch (error) {
    logger.error('Error getting Alice AGI status:', error);
    
    return res.status(500).json({
      success: false,
      message: 'Error getting Alice AGI status',
      error: error instanceof Error ? error.message : String(error)
    });
  }
});

/**
 * Health check endpoint for network manager
 *
 * @route GET /api/system/health
 * @group System - Health check for network coordination
 * @returns {object} 200 - Health status
 * @returns {Error} 500 - Server error
 */
router.get('/system/health', async (req: Request, res: Response) => {
  try {
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      instanceId: process.env.ALICE_INSTANCE_ID || 'Alice_main',
      role: process.env.ALICE_ROLE || 'coordinator',
      port: process.env.PORT || 8003,
      uptime: process.uptime(),
      memory: {
        used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
        total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024)
      },
      initialized: aliceAGIInitialized,
      systemsCount: Object.keys(aliceAGIInitialized ? {} : {}).length
    };

    res.json(health);
  } catch (error) {
    console.error('Error getting health status:', error);
    res.status(500).json({
      status: 'unhealthy',
      error: error instanceof Error ? error.message : String(error),
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Interact with Alice AGI
 * 
 * @route POST /api/alice-agi/interact
 * @group Alice AGI - Operations related to Alice AGI
 * @param {object} req.body.input - Input for Alice AGI
 * @returns {object} 200 - Success response
 * @returns {Error} 500 - Server error
 */
router.post('/interact', async (req: Request, res: Response) => {
  try {
    if (!aliceAGIInitialized) {
      return res.status(400).json({
        success: false,
        message: 'Alice AGI is not initialized'
      });
    }
    
    const { input } = req.body;
    
    if (!input) {
      return res.status(400).json({
        success: false,
        message: 'Input is required'
      });
    }
    
    logger.info('Processing interaction with Alice AGI...');
    
    // Process the input using the appropriate systems
    let response;

    if ((global as any).interactionController) {
      // Use Interaction Controller if available
      const sessionId = (req.session as any)?.id || (global as any).interactionController.startInteractionSession('api-user');

      if (!(req.session as any)?.id) {
        (req.session as any).id = sessionId;
      }

      const result = await (global as any).interactionController.generateResponse(sessionId, input);
      response = result.response;
    } else if ((global as any).multimodalLLMConnector) {
      // Use Multimodal LLM Connector if available
      const conversationId = (req.session as any)?.conversationId || (global as any).multimodalLLMConnector.createConversation('gpt-4');

      if (!(req.session as any)?.conversationId) {
        (req.session as any).conversationId = conversationId;
      }

      (global as any).multimodalLLMConnector.addMessage(conversationId, 'user', input);
      const result = await (global as any).multimodalLLMConnector.generateResponse(conversationId);
      response = result.content;
    } else {
      // Fallback to a simple response
      response = 'Alice AGI is running, but no interaction system is available.';
    }
    
    return res.status(200).json({
      success: true,
      response
    });
  } catch (error) {
    logger.error('Error interacting with Alice AGI:', error);
    
    return res.status(500).json({
      success: false,
      message: 'Error interacting with Alice AGI',
      error: error instanceof Error ? error.message : String(error)
    });
  }
});

/**
 * Shutdown Alice AGI
 * 
 * @route POST /api/alice-agi/shutdown
 * @group Alice AGI - Operations related to Alice AGI
 * @returns {object} 200 - Success response
 * @returns {Error} 500 - Server error
 */
router.post('/shutdown', async (req: Request, res: Response) => {
  try {
    if (!aliceAGIInitialized) {
      return res.status(400).json({
        success: false,
        message: 'Alice AGI is not initialized'
      });
    }
    
    logger.info('Shutting down Alice AGI from API request...');
    
    // Perform orderly shutdown
    await performOrderlyShutdown();
    aliceAGIInitialized = false;
    
    return res.status(200).json({
      success: true,
      message: 'Alice AGI shut down successfully'
    });
  } catch (error) {
    logger.error('Error shutting down Alice AGI:', error);
    
    return res.status(500).json({
      success: false,
      message: 'Error shutting down Alice AGI',
      error: error instanceof Error ? error.message : String(error)
    });
  }
});

/**
 * Perform an orderly shutdown of all systems
 */
async function performOrderlyShutdown(): Promise<void> {
  logger.info('Performing orderly shutdown of all systems...');
  
  try {
    // Shutdown in reverse order of initialization
    
    // Shutdown Multimodal Systems
    if ((global as any).userInterfaceAdapter) {
      logger.info('Shutting down User Interface Adapter...');
      await (global as any).userInterfaceAdapter.shutdown?.();
    }

    if ((global as any).interactionController) {
      logger.info('Shutting down Interaction Controller...');
      await (global as any).interactionController.shutdown?.();
    }

    if ((global as any).multimodalPerceptionEngine) {
      logger.info('Shutting down Multimodal Perception Engine...');
      await (global as any).multimodalPerceptionEngine.shutdown?.();
    }

    // Shutdown Quantum Systems
    if ((global as any).quantumNetworkNodes) {
      logger.info('Shutting down Quantum Network Nodes...');
      await (global as any).quantumNetworkNodes.shutdown?.();
    }

    if ((global as any).quantumMemoryStorage) {
      logger.info('Shutting down Quantum Memory Storage...');
      await (global as any).quantumMemoryStorage.shutdown?.();
    }
    
    // Shutdown Neural ML Systems
    if ((global as any).neuralEvolutionSystem) {
      logger.info('Shutting down Neural Evolution System...');
      await (global as any).neuralEvolutionSystem.shutdown?.();
    }

    if ((global as any).reinforcementLearningSystem) {
      logger.info('Shutting down Reinforcement Learning System...');
      await (global as any).reinforcementLearningSystem.shutdown?.();
    }

    // Shutdown LLM Integration
    if ((global as any).multimodalLLMConnector) {
      logger.info('Shutting down Multimodal LLM Connector...');
      await (global as any).multimodalLLMConnector.shutdown?.();
    }

    // Shutdown Agent Systems
    if ((global as any).dreamCivilizationSimulator) {
      logger.info('Shutting down Dream Civilization Simulator...');
      await (global as any).dreamCivilizationSimulator.shutdown?.();
    }

    if ((global as any).goalWeaverAgent) {
      logger.info('Shutting down Goal Weaver Agent...');
      await (global as any).goalWeaverAgent.shutdown?.();
    }
    
    // Shutdown Cognitive Systems
    if ((global as any).quantumConsciousnessAmplifier) {
      logger.info('Shutting down Quantum Consciousness Amplifier...');
      await (global as any).quantumConsciousnessAmplifier.shutdown?.();
    }

    if ((global as any).consciousnessModel) {
      logger.info('Shutting down Consciousness Model...');
      await (global as any).consciousnessModel.shutdown?.();
    }

    // Shutdown Biological Systems
    if ((global as any).dreamSystem) {
      logger.info('Shutting down Dream System...');
      await (global as any).dreamSystem.shutdown?.();
    }

    if ((global as any).hyperMind) {
      logger.info('Shutting down HyperMind...');
      await (global as any).hyperMind.shutdown?.();
    }

    if ((global as any).memoryForest) {
      logger.info('Shutting down Memory Forest...');
      await (global as any).memoryForest.shutdown?.();
    }
    
    // Shutdown Core Infrastructure
    if ((global as any).transactionalMemory) {
      logger.info('Shutting down Transactional Memory...');
      await (global as any).transactionalMemory.shutdown?.();
    }

    if ((global as any).memorySystem) {
      logger.info('Shutting down Memory System...');
      await (global as any).memorySystem.shutdown?.();
    }

    if ((global as any).blackboardSystem) {
      logger.info('Shutting down Blackboard System...');
      await (global as any).blackboardSystem.shutdown?.();
    }

    if ((global as any).systemIntegrator) {
      logger.info('Shutting down System Integrator...');
      await (global as any).systemIntegrator.shutdown?.();
    }
    
    logger.info('All systems have been shut down successfully');
  } catch (error) {
    logger.error('Error during orderly shutdown:', error);
    throw error;
  }
}

/**
 * BROWSER INTEGRATION API ENDPOINTS
 * These endpoints bridge the frontend browser components with Alice's MCP Playwright tools
 */

/**
 * Create browser session
 * @route POST /api/alice-agi/browser/create-session
 */
router.post('/browser/create-session', async (req: Request, res: Response) => {
  try {
    const { chatId, url } = req.body;

    if (!chatId) {
      return res.status(400).json({
        success: false,
        error: 'Chat ID is required'
      });
    }

    const integration = (global as any).aliceBiologicalMCPIntegration;

    if (!integration) {
      return res.status(400).json({
        success: false,
        error: 'Alice Biological MCP Integration not initialized'
      });
    }

    logger.info(`🌐 Creating browser session for chat ${chatId} with URL: ${url || 'https://google.com'}`);

    // Create a session ID
    const sessionId = `session_${chatId}_${Date.now()}`;

    // Navigate to the URL using MCP Playwright
    if (url) {
      await integration.controlBrowser('navigate', { url });
    }

    res.json({
      success: true,
      sessionId,
      message: 'Browser session created successfully'
    });

  } catch (error) {
    logger.error('❌ Browser session creation error:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Session creation failed'
    });
  }
});

/**
 * Destroy browser session
 * @route POST /api/alice-agi/browser/destroy-session
 */
router.post('/browser/destroy-session', async (req: Request, res: Response) => {
  try {
    const { sessionId } = req.body;

    if (!sessionId) {
      return res.status(400).json({
        success: false,
        error: 'Session ID is required'
      });
    }

    logger.info(`🗑️ Destroying browser session: ${sessionId}`);

    res.json({
      success: true,
      message: 'Browser session destroyed successfully'
    });

  } catch (error) {
    logger.error('❌ Browser session destruction error:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Session destruction failed'
    });
  }
});

/**
 * Navigate browser
 * @route POST /api/alice-agi/browser/navigate
 */
router.post('/browser/navigate', async (req: Request, res: Response) => {
  try {
    const { url, sessionId, waitUntil } = req.body;

    if (!url) {
      return res.status(400).json({
        success: false,
        error: 'URL is required'
      });
    }

    const integration = (global as any).aliceBiologicalMCPIntegration;

    if (!integration) {
      return res.status(400).json({
        success: false,
        error: 'Alice Biological MCP Integration not initialized'
      });
    }

    logger.info(`🌐 Alice navigating to: ${url}`);

    // Navigate using MCP Playwright
    const navigationResult = await integration.controlBrowser('navigate', { url });

    if (navigationResult.success) {
      // Take a screenshot for external websites
      try {
        const screenshotResult = await integration.controlBrowser('take_screenshot', {
          filename: `alice-navigation-${Date.now()}.png`
        });

        if (screenshotResult.success && screenshotResult.result?.buffer) {
          res.json({
            success: true,
            result: {
              useScreenshot: true,
              screenshot: screenshotResult.result.buffer,
              url: url
            },
            message: 'Navigation successful with screenshot'
          });
        } else {
          res.json({
            success: true,
            result: {
              useScreenshot: false,
              url: url
            },
            message: 'Navigation successful'
          });
        }
      } catch (screenshotError) {
        logger.warn('Screenshot failed after navigation:', screenshotError);
        res.json({
          success: true,
          result: {
            useScreenshot: false,
            url: url
          },
          message: 'Navigation successful (screenshot failed)'
        });
      }
    } else {
      res.json({
        success: false,
        error: 'Navigation failed'
      });
    }

  } catch (error) {
    logger.error('❌ Browser navigation error:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Navigation failed'
    });
  }
});

/**
 * Take browser screenshot
 * @route POST /api/alice-agi/browser/screenshot
 */
router.post('/browser/screenshot', async (req: Request, res: Response) => {
  try {
    const { sessionId, fullPage } = req.body;

    const integration = (global as any).aliceBiologicalMCPIntegration;

    if (!integration) {
      return res.status(400).json({
        success: false,
        error: 'Alice Biological MCP Integration not initialized'
      });
    }

    logger.info('📷 Alice taking screenshot...');

    const result = await integration.controlBrowser('take_screenshot', {
      filename: `alice-screenshot-${Date.now()}.png`,
      fullPage: fullPage || false
    });

    res.json(result);

  } catch (error) {
    logger.error('❌ Browser screenshot error:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Screenshot failed'
    });
  }
});

/**
 * REFLEXIVE SELF-EVOLUTION API ENDPOINTS
 */

/**
 * Start reflexive evolution loop
 *
 * @route POST /api/alice-agi/reflexive-evolution/start
 * @group Reflexive Evolution - Self-improvement operations
 * @returns {object} 200 - Success response
 * @returns {Error} 500 - Server error
 */
router.post('/reflexive-evolution/start', async (req: Request, res: Response) => {
  try {
    if (!(global as any).reflexiveSelfEvolutionSystem) {
      return res.status(400).json({
        success: false,
        message: 'ReflexiveSelfEvolutionSystem is not initialized'
      });
    }

    await (global as any).reflexiveSelfEvolutionSystem.startReflexiveLoop();

    return res.status(200).json({
      success: true,
      message: 'Reflexive evolution loop started successfully'
    });
  } catch (error) {
    logger.error('Error starting reflexive evolution:', error);
    return res.status(500).json({
      success: false,
      message: 'Error starting reflexive evolution',
      error: error instanceof Error ? error.message : String(error)
    });
  }
});

/**
 * Stop reflexive evolution loop
 *
 * @route POST /api/alice-agi/reflexive-evolution/stop
 * @group Reflexive Evolution - Self-improvement operations
 * @returns {object} 200 - Success response
 * @returns {Error} 500 - Server error
 */
router.post('/reflexive-evolution/stop', async (req: Request, res: Response) => {
  try {
    if (!(global as any).reflexiveSelfEvolutionSystem) {
      return res.status(400).json({
        success: false,
        message: 'ReflexiveSelfEvolutionSystem is not initialized'
      });
    }

    await (global as any).reflexiveSelfEvolutionSystem.stopReflexiveLoop();

    return res.status(200).json({
      success: true,
      message: 'Reflexive evolution loop stopped successfully'
    });
  } catch (error) {
    logger.error('Error stopping reflexive evolution:', error);
    return res.status(500).json({
      success: false,
      message: 'Error stopping reflexive evolution',
      error: error instanceof Error ? error.message : String(error)
    });
  }
});

/**
 * Get reflexive evolution status
 *
 * @route GET /api/alice-agi/reflexive-evolution/status
 * @group Reflexive Evolution - Self-improvement operations
 * @returns {object} 200 - Success response
 * @returns {Error} 500 - Server error
 */
router.get('/reflexive-evolution/status', (req: Request, res: Response) => {
  try {
    const status = {
      reflexiveSystem: (global as any).reflexiveSelfEvolutionSystem?.getSystemStatus() || null,
      selfAwareness: (global as any).selfAwarenessMonitor?.getSystemStatus() || null,
      patchManager: (global as any).autonomousPatchManager?.getSystemStatus() || null
    };

    return res.status(200).json({
      success: true,
      status
    });
  } catch (error) {
    logger.error('Error getting reflexive evolution status:', error);
    return res.status(500).json({
      success: false,
      message: 'Error getting reflexive evolution status',
      error: error instanceof Error ? error.message : String(error)
    });
  }
});

/**
 * Start mirror window for self-observation
 *
 * @route POST /api/alice-agi/reflexive-evolution/start-mirror
 * @group Reflexive Evolution - Self-improvement operations
 * @returns {object} 200 - Success response
 * @returns {Error} 500 - Server error
 */
router.post('/reflexive-evolution/start-mirror', async (req: Request, res: Response) => {
  try {
    if (!(global as any).selfAwarenessMonitor) {
      return res.status(400).json({
        success: false,
        message: 'SelfAwarenessMonitor is not initialized'
      });
    }

    await (global as any).selfAwarenessMonitor.startMonitoring();

    return res.status(200).json({
      success: true,
      message: 'Mirror window started - Alice is now observing herself',
      mirrorUrl: 'http://localhost:3013'
    });
  } catch (error) {
    logger.error('Error starting mirror window:', error);
    return res.status(500).json({
      success: false,
      message: 'Error starting mirror window',
      error: error instanceof Error ? error.message : String(error)
    });
  }
});

/**
 * Get self-awareness insights
 *
 * @route GET /api/alice-agi/reflexive-evolution/insights
 * @group Reflexive Evolution - Self-improvement operations
 * @returns {object} 200 - Success response
 * @returns {Error} 500 - Server error
 */
router.get('/reflexive-evolution/insights', (req: Request, res: Response) => {
  try {
    if (!(global as any).selfAwarenessMonitor) {
      return res.status(400).json({
        success: false,
        message: 'SelfAwarenessMonitor is not initialized'
      });
    }

    const status = (global as any).selfAwarenessMonitor.getSystemStatus();

    return res.status(200).json({
      success: true,
      insights: status.insights || 0,
      conversations: status.conversations || 0,
      metrics: status.metrics || {},
      monitoring: status.monitoring || false
    });
  } catch (error) {
    logger.error('Error getting self-awareness insights:', error);
    return res.status(500).json({
      success: false,
      message: 'Error getting self-awareness insights',
      error: error instanceof Error ? error.message : String(error)
    });
  }
});

/**
 * Execute browser action in Alice's mirror window
 *
 * @route POST /api/alice-agi/browser/execute
 * @group Browser Control - Alice's autonomous browser actions
 * @returns {object} 200 - Success response
 * @returns {Error} 500 - Server error
 */
router.post('/browser/execute', async (req: Request, res: Response) => {
  try {
    if (!(global as any).selfAwarenessMonitor) {
      return res.status(400).json({
        success: false,
        message: 'SelfAwarenessMonitor is not initialized'
      });
    }

    const { action, url, selector, text, options = {} } = req.body;

    if (!action) {
      return res.status(400).json({
        success: false,
        message: 'Action is required'
      });
    }

    const result = await (global as any).selfAwarenessMonitor.executeBrowserAction({
      action,
      url,
      selector,
      text,
      options
    });

    // Convert buffer to base64 for frontend consumption if it's a screenshot
    if (result.buffer && Buffer.isBuffer(result.buffer)) {
      result.buffer = result.buffer.toString('base64');
    }

    return res.status(200).json({
      success: true,
      message: 'Browser action executed successfully',
      result
    });
  } catch (error) {
    console.error('Error executing browser action:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to execute browser action',
      error: error instanceof Error ? error.message : String(error)
    });
  }
});

/**
 * Take screenshot of Alice's mirror window
 *
 * @route POST /api/alice-agi/browser/screenshot
 * @group Browser Control - Alice's autonomous browser actions
 * @returns {object} 200 - Success response
 * @returns {Error} 500 - Server error
 */
router.post('/browser/screenshot', async (req: Request, res: Response) => {
  try {
    if (!(global as any).selfAwarenessMonitor) {
      return res.status(400).json({
        success: false,
        message: 'SelfAwarenessMonitor is not initialized'
      });
    }

    const { filename, sessionId, fullPage = false } = req.body;

    const result = await (global as any).selfAwarenessMonitor.takeScreenshot({
      filename,
      fullPage
    });

    // Convert buffer to base64 for frontend consumption
    if (result.buffer && Buffer.isBuffer(result.buffer)) {
      result.buffer = result.buffer.toString('base64');
    }

    return res.status(200).json({
      success: true,
      message: 'Screenshot taken successfully',
      result
    });
  } catch (error) {
    console.error('Error taking screenshot:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to take screenshot',
      error: error instanceof Error ? error.message : String(error)
    });
  }
});

/**
 * Navigate Alice's mirror window to a URL
 *
 * @route POST /api/alice-agi/browser/navigate
 * @group Browser Control - Alice's autonomous browser actions
 * @returns {object} 200 - Success response
 * @returns {Error} 500 - Server error
 */
router.post('/browser/navigate', async (req: Request, res: Response) => {
  try {
    const { url, sessionId, waitUntil = 'networkidle' } = req.body;

    if (!url) {
      return res.status(400).json({
        success: false,
        message: 'URL is required'
      });
    }

    // Validate URL format first
    try {
      const urlObj = new URL(url);

      // For security, only allow http/https protocols
      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        return res.status(400).json({
          success: false,
          message: 'Only HTTP and HTTPS URLs are allowed'
        });
      }
    } catch (urlError) {
      return res.status(400).json({
        success: false,
        message: 'Invalid URL format',
        error: urlError instanceof Error ? urlError.message : String(urlError)
      });
    }

    // Use Alice's real Playwright MCP Server for navigation
    if ((global as any).aliceBiologicalMCPIntegration) {
      try {
        console.log(`🌐 Navigating to ${url} with Alice's Biological MCP Integration...`);

        // Navigate using Alice's real Playwright MCP tools via BiologicalMCPIntegration
        const mcpIntegration = (global as any).aliceBiologicalMCPIntegration;

        // Execute browser navigation using MCP tool
        const navigationResult = await mcpIntegration.executeMCPTool('browser_navigate', { url: url });

        if (navigationResult && navigationResult.success) {
          // Automatically take a screenshot after navigation
          const screenshotResult = await mcpIntegration.executeMCPTool('browser_take_screenshot', {
            filename: `navigation-${Date.now()}.png`
          });

          return res.status(200).json({
            success: true,
            message: 'Navigation completed successfully with real Playwright MCP',
            result: {
              url: url,
              sessionId: navigationResult.result?.sessionId || `session_${Date.now()}`,
              timestamp: new Date().toISOString(),
              title: navigationResult.result?.title || `Page at ${new URL(url).hostname}`,
              screenshot: screenshotResult.result?.buffer, // Base64 screenshot
              navigated: true,
              useScreenshot: true // Tell frontend to use screenshot mode
            }
          });
        }
      } catch (browserError) {
        console.warn('Alice Biological MCP navigation failed, falling back to validation mode:', browserError);
      }
    }

    // Fallback: Return success with validation only (for iframe mode)
    return res.status(200).json({
      success: true,
      message: 'Navigation URL validated successfully (fallback mode)',
      result: {
        url: url,
        sessionId: sessionId || `session_${Date.now()}`,
        timestamp: new Date().toISOString(),
        title: `Page at ${new URL(url).hostname}`,
        validated: true,
        useScreenshot: false // Tell frontend to try iframe mode
      }
    });
  } catch (error) {
    console.error('Error processing navigation:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to process navigation',
      error: error instanceof Error ? error.message : String(error)
    });
  }
});

/**
 * Create a new browser session for a conversation
 *
 * @route POST /api/alice-agi/browser/create-session
 * @group Browser Control - Conversation-specific browser sessions
 * @returns {object} 200 - Success response
 * @returns {Error} 500 - Server error
 */
router.post('/browser/create-session', async (req: Request, res: Response) => {
  try {
    const { chatId = 'default', url = 'https://google.com' } = req.body;

    // Always create a session, even without chatId
    console.log(`🌐 Creating browser session for chat: ${chatId}, URL: ${url}`);

    // Create actual browser session using SelfAwarenessMonitor
    if (!(global as any).selfAwarenessMonitor) {
      return res.status(400).json({
        success: false,
        message: 'SelfAwarenessMonitor is not initialized'
      });
    }

    try {
      // Create a new browser session for this chat
      const sessionId = `session_${chatId}_${Date.now()}`;

      // Initialize browser session in SelfAwarenessMonitor
      const result = await (global as any).selfAwarenessMonitor.createBrowserSession({
        sessionId,
        chatId,
        initialUrl: url
      });

      if (result.success) {
        return res.status(200).json({
          success: true,
          message: 'Browser session created successfully',
          sessionId: result.sessionId, // Use the actual session ID returned
          chatId,
          initialUrl: url,
          browserResult: result
        });
      } else {
        throw new Error(result.error || 'Failed to create browser session');
      }
    } catch (browserError) {
      // Fallback to simple session ID if browser creation fails
      console.warn('Browser session creation failed, using fallback:', browserError);
      const sessionId = `session_${chatId}_${Date.now()}`;

      return res.status(200).json({
        success: true,
        message: 'Browser session created successfully (fallback mode)',
        sessionId,
        chatId,
        initialUrl: url,
        fallback: true
      });
    }
  } catch (error) {
    console.error('Error creating browser session:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to create browser session',
      error: error instanceof Error ? error.message : String(error)
    });
  }
});

// Session destruction throttling
const sessionDestroyThrottle = new Map<string, number>();
const DESTROY_THROTTLE_MS = 10000; // 10 seconds (increased)
const destroyedSessions = new Set<string>(); // Track already destroyed sessions

// Cleanup old entries every 5 minutes
setInterval(() => {
  const now = Date.now();
  const cutoff = now - (5 * 60 * 1000); // 5 minutes ago

  // Clean up throttle map
  for (const [sessionId, timestamp] of sessionDestroyThrottle.entries()) {
    if (timestamp < cutoff) {
      sessionDestroyThrottle.delete(sessionId);
      destroyedSessions.delete(sessionId);
    }
  }
}, 5 * 60 * 1000); // Every 5 minutes

/**
 * Destroy a browser session
 *
 * @route POST /api/alice-agi/browser/destroy-session
 * @group Browser Control - Conversation-specific browser sessions
 * @returns {object} 200 - Success response
 * @returns {Error} 500 - Server error
 */
router.post('/browser/destroy-session', async (req: Request, res: Response) => {
  try {
    const { sessionId } = req.body;

    if (!sessionId) {
      return res.status(400).json({
        success: false,
        message: 'Session ID is required'
      });
    }

    // Check if already destroyed
    if (destroyedSessions.has(sessionId)) {
      return res.status(200).json({
        success: true,
        message: 'Session already destroyed',
        sessionId,
        alreadyDestroyed: true
      });
    }

    // Check throttling to prevent spam
    const now = Date.now();
    const lastDestroy = sessionDestroyThrottle.get(sessionId);

    if (lastDestroy && (now - lastDestroy) < DESTROY_THROTTLE_MS) {
      return res.status(200).json({
        success: true,
        message: 'Session destruction throttled (already processed recently)',
        sessionId,
        throttled: true
      });
    }

    // Update throttle timestamp and mark as destroyed
    sessionDestroyThrottle.set(sessionId, now);
    destroyedSessions.add(sessionId);

    // Try to destroy browser session using SelfAwarenessMonitor if available
    if ((global as any).selfAwarenessMonitor) {
      try {
        const result = await (global as any).selfAwarenessMonitor.destroyBrowserSession(sessionId);

        if (result && result.success) {
          return res.status(200).json({
            success: true,
            message: 'Browser session destroyed successfully',
            sessionId
          });
        }
      } catch (error) {
        console.warn('SelfAwarenessMonitor destroy failed, using fallback:', error);
      }
    }

    // Fallback: acknowledge session destruction without actual cleanup
    return res.status(200).json({
      success: true,
      message: 'Browser session destroyed successfully (fallback mode)',
      sessionId,
      fallback: true
    });
  } catch (error) {
    console.error('Error destroying browser session:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to destroy browser session',
      error: error instanceof Error ? error.message : String(error)
    });
  }
});

/**
 * MULTI-MODAL RECURSIVE SELF-IMPROVEMENT SYSTEM ENDPOINTS
 */

// Initialize the multi-modal recursive system
let multiModalRecursiveSystem: MultiModalRecursiveSystem | null = null;

function getMultiModalSystem(): MultiModalRecursiveSystem {
  if (!multiModalRecursiveSystem) {
    multiModalRecursiveSystem = new MultiModalRecursiveSystem();
    (global as any).multiModalRecursiveSystem = multiModalRecursiveSystem;
  }
  return multiModalRecursiveSystem;
}

/**
 * Start a new recursive conversation
 * @route POST /api/alice-agi/recursive/start-conversation
 */
router.post('/recursive/start-conversation', async (req: Request, res: Response) => {
  try {
    const { modeId, initialPrompt } = req.body;

    if (!modeId) {
      return res.status(400).json({
        success: false,
        message: 'modeId is required'
      });
    }

    const system = getMultiModalSystem();
    const conversationId = await system.startConversation(modeId, initialPrompt);

    return res.status(200).json({
      success: true,
      conversationId,
      message: 'Recursive conversation started successfully'
    });
  } catch (error) {
    logger.error('Error starting recursive conversation:', error);
    return res.status(500).json({
      success: false,
      message: 'Error starting recursive conversation',
      error: error instanceof Error ? error.message : String(error)
    });
  }
});

/**
 * Add message to recursive conversation
 * @route POST /api/alice-agi/recursive/add-message
 */
router.post('/recursive/add-message', async (req: Request, res: Response) => {
  try {
    const { conversationId, sender, content, metadata } = req.body;

    if (!conversationId || !sender || !content) {
      return res.status(400).json({
        success: false,
        message: 'conversationId, sender, and content are required'
      });
    }

    const system = getMultiModalSystem();
    await system.addMessage(conversationId, sender, content, metadata || {});

    return res.status(200).json({
      success: true,
      message: 'Message added successfully'
    });
  } catch (error) {
    logger.error('Error adding message to conversation:', error);
    return res.status(500).json({
      success: false,
      message: 'Error adding message to conversation',
      error: error instanceof Error ? error.message : String(error)
    });
  }
});

/**
 * Get conversation status
 * @route GET /api/alice-agi/recursive/conversation/:id
 */
router.get('/recursive/conversation/:id', (req: Request, res: Response) => {
  try {
    const conversationId = req.params.id;
    const system = getMultiModalSystem();
    const conversation = system.getConversationStatus(conversationId);

    if (!conversation) {
      return res.status(404).json({
        success: false,
        message: 'Conversation not found'
      });
    }

    return res.status(200).json({
      success: true,
      conversation
    });
  } catch (error) {
    logger.error('Error getting conversation status:', error);
    return res.status(500).json({
      success: false,
      message: 'Error getting conversation status',
      error: error instanceof Error ? error.message : String(error)
    });
  }
});

/**
 * Get all active conversations
 * @route GET /api/alice-agi/recursive/conversations
 */
router.get('/recursive/conversations', (req: Request, res: Response) => {
  try {
    const system = getMultiModalSystem();
    const conversations = system.getActiveConversations();

    return res.status(200).json({
      success: true,
      conversations
    });
  } catch (error) {
    logger.error('Error getting active conversations:', error);
    return res.status(500).json({
      success: false,
      message: 'Error getting active conversations',
      error: error instanceof Error ? error.message : String(error)
    });
  }
});

/**
 * Get available conversation modes
 * @route GET /api/alice-agi/recursive/modes
 */
router.get('/recursive/modes', (req: Request, res: Response) => {
  try {
    const system = getMultiModalSystem();
    const modes = system.getAvailableModes();

    return res.status(200).json({
      success: true,
      modes
    });
  } catch (error) {
    logger.error('Error getting conversation modes:', error);
    return res.status(500).json({
      success: false,
      message: 'Error getting conversation modes',
      error: error instanceof Error ? error.message : String(error)
    });
  }
});

/**
 * Stop a conversation
 * @route POST /api/alice-agi/recursive/stop-conversation
 */
router.post('/recursive/stop-conversation', async (req: Request, res: Response) => {
  try {
    const { conversationId } = req.body;

    if (!conversationId) {
      return res.status(400).json({
        success: false,
        message: 'conversationId is required'
      });
    }

    const system = getMultiModalSystem();
    await system.stopConversation(conversationId);

    return res.status(200).json({
      success: true,
      message: 'Conversation stopped successfully'
    });
  } catch (error) {
    logger.error('Error stopping conversation:', error);
    return res.status(500).json({
      success: false,
      message: 'Error stopping conversation',
      error: error instanceof Error ? error.message : String(error)
    });
  }
});

export default router;

