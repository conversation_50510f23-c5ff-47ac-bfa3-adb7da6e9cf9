import React, { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  XMarkIcon,
  ArrowsPointingOutIcon,
  CameraIcon,
  ArrowPathIcon,
  GlobeAltIcon,
} from '@heroicons/react/24/outline';
import { useBrowserDetachment } from '../../contexts/BrowserDetachmentContext';
import { useChat } from '../../contexts/ChatContext';

interface NestedBrowserWindowProps {
  messageId: string;
  initialUrl: string;
  onDetach: () => void;
  onClose: () => void;
}

export const NestedBrowserWindow: React.FC<NestedBrowserWindowProps> = ({
  messageId,
  initialUrl,
  onDetach,
  onClose,
}) => {
  const {
    globalBrowserState,
    updateGlobalBrowser,
    updateBrowserStateForChat,
    createBrowserSession,
    destroyBrowserSession
  } = useBrowserDetachment();
  const { currentChat } = useChat();
  const [isLoading, setIsLoading] = useState(false);
  const [currentUrl, setCurrentUrl] = useState(initialUrl);
  const [screenshotUrl, setScreenshotUrl] = useState<string | null>(null);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [showLiveView, setShowLiveView] = useState(true);
  const messageRef = useRef<HTMLDivElement>(null);
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [iframeUrl, setIframeUrl] = useState(initialUrl || 'https://google.com'); // Separate state for iframe src

  // Handle scroll detection for auto-detachment
  useEffect(() => {
    const handleScroll = () => {
      if (!messageRef.current) return;

      const rect = messageRef.current.getBoundingClientRect();
      const headerHeight = 80; // Account for header height

      // Detach when the message (containing the browser window) scrolls above the header
      const shouldDetach = rect.top < headerHeight && rect.bottom > headerHeight;

      if (shouldDetach) {
        onDetach();
      }
    };

    // Initial check
    handleScroll();

    window.addEventListener('scroll', handleScroll);
    window.addEventListener('resize', handleScroll);

    return () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('resize', handleScroll);
    };
  }, [onDetach]);

  // Create browser session and navigate to initial URL on mount
  useEffect(() => {
    if (initialUrl && initialUrl !== 'about:blank' && currentChat) {
      const initializeBrowser = async () => {
        setIsLoading(true);
        try {
          // Create a conversation-specific browser session
          const newSessionId = await createBrowserSession(currentChat.id, initialUrl);
          setSessionId(newSessionId);
          setIsConnected(true);

          // Navigate to initial URL with session
          const response = await fetch('/api/alice-agi/browser/navigate', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              url: initialUrl,
              sessionId: newSessionId,
              waitUntil: 'networkidle'
            }),
          });

          const result = await response.json();

          if (result.success) {
            // Update conversation browser state
            updateBrowserStateForChat(currentChat.id, {
              currentUrl: result.result?.url || initialUrl,
              isConnected: true,
              sessionId: newSessionId
            });

            // Ensure we show the live view with the URL
            const finalUrl = result.result?.url || initialUrl;
            setCurrentUrl(finalUrl);
            setIframeUrl(finalUrl);
            setShowLiveView(true);

            // No automatic screenshots - live view is preferred
            // Screenshots only taken when explicitly requested by user
          }
        } catch (error) {
          console.error('Initial browser setup failed:', error);
          setIsConnected(false);
          // Still show the live view even if backend setup fails
          setCurrentUrl(initialUrl);
          setIframeUrl(initialUrl);
          setShowLiveView(true);
        } finally {
          setIsLoading(false);
        }
      };

      initializeBrowser();
    } else if (initialUrl && initialUrl !== 'about:blank') {
      // If no currentChat, still show the browser with the URL
      setCurrentUrl(initialUrl);
      setIframeUrl(initialUrl);
      setShowLiveView(true);
      setIsLoading(false);
    }
  }, [initialUrl, currentChat]);

  // Cleanup browser session on unmount (only if backend is available)
  useEffect(() => {
    return () => {
      if (sessionId) {
        // Only attempt cleanup if we have a valid session and it's not a page refresh
        const isPageRefresh = performance.navigation?.type === 1;
        if (!isPageRefresh) {
          destroyBrowserSession(sessionId).catch(() => {
            // Silently handle errors - backend may be offline
          });
        }
      }
    };
  }, [sessionId]);

  // Auto-detach on scroll functionality
  useEffect(() => {
    const handleScroll = () => {
      if (!messageRef.current) return;

      const rect = messageRef.current.getBoundingClientRect();
      const isVisible = rect.top >= 0 && rect.bottom <= window.innerHeight;

      // If browser is not expanded and scrolled out of view, detach it
      if (!isExpanded && !isVisible && rect.top < 0) {
        onDetach();
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [isExpanded, onDetach]);

  const handleTakeScreenshot = async () => {
    if (!sessionId || !currentChat) return;

    setIsLoading(true);
    try {
      // Use Alice's backend browser API for screenshot with session
      const response = await fetch('/api/alice-agi/browser/screenshot', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sessionId,
          fullPage: false
        }),
      });

      const result = await response.json();

      if (result.success && result.result?.buffer) {
        // Backend already converts buffer to base64 string
        const screenshotDataUrl = `data:image/png;base64,${result.result.buffer}`;
        setScreenshotUrl(screenshotDataUrl);

        // Update conversation-specific browser state
        updateBrowserStateForChat(currentChat.id, {
          screenshot: screenshotDataUrl,
          lastActivity: new Date()
        });

        // Update global browser with screenshot
        updateGlobalBrowser({
          screenshot: screenshotDataUrl,
        });
      }
    } catch (error) {
      console.error('Error taking screenshot:', error);
      setIsConnected(false);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = () => {
    setIsLoading(true);
    // Force iframe reload using ref
    if (iframeRef.current) {
      const currentSrc = iframeRef.current.src;
      iframeRef.current.src = 'about:blank';
      setTimeout(() => {
        if (iframeRef.current) {
          iframeRef.current.src = currentSrc;
        }
      }, 100);
    }
    setTimeout(() => setIsLoading(false), 1000);
  };

  const handleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  const handleToggleView = () => {
    setShowLiveView(!showLiveView);
    // Only take screenshot when switching TO screenshot mode and we don't have one
    if (showLiveView && !screenshotUrl) {
      handleTakeScreenshot();
    }
  };

  const handleUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCurrentUrl(e.target.value);
  };

  const handleUrlSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!sessionId || !currentChat) return;

    setIsLoading(true);
    try {
      // Use Alice's backend browser API for navigation with session
      const response = await fetch('/api/alice-agi/browser/navigate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          url: currentUrl,
          sessionId,
          waitUntil: 'networkidle'
        }),
      });

      const result = await response.json();

      if (result.success) {
        const finalUrl = result.result?.url || currentUrl;

        // Update conversation-specific browser state
        updateBrowserStateForChat(currentChat.id, {
          currentUrl: finalUrl,
          lastActivity: new Date()
        });

        updateGlobalBrowser({ currentUrl: finalUrl });

        // Check if backend provided a screenshot (Playwright navigation)
        if (result.result?.useScreenshot && result.result?.screenshot) {
          console.log('🎯 Using Playwright screenshot mode for external website');

          // Use screenshot mode for external websites
          const screenshotDataUrl = `data:image/png;base64,${result.result.screenshot}`;
          setScreenshotUrl(screenshotDataUrl);
          setShowLiveView(false); // Use screenshot mode instead of iframe

          // Update browser state with screenshot
          updateBrowserStateForChat(currentChat.id, {
            screenshot: screenshotDataUrl,
            lastActivity: new Date()
          });
        } else {
          console.log('🌐 Using iframe mode for URL navigation');

          // Use iframe mode (for internal URLs or when Playwright fails)
          setCurrentUrl(finalUrl);
          setIframeUrl(finalUrl);
          setShowLiveView(true);
        }
      } else {
        // Even if backend validation fails, still show the live view
        setShowLiveView(true);
      }
    } catch (error) {
      console.error('Navigation failed:', error);
      setIsConnected(false);
      // Still show live view even if there's an error
      setShowLiveView(true);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <motion.div
      ref={messageRef}
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{
        opacity: 1,
        scale: 1,
        height: isExpanded ? 'auto' : 'auto'
      }}
      exit={{ opacity: 0, scale: 0.95 }}
      transition={{ duration: 0.3 }}
      className={`mt-4 border border-light-border dark:border-dark-border rounded-lg overflow-hidden bg-light-background-primary dark:bg-dark-background-primary shadow-lg ${isExpanded ? 'z-10' : ''}`}
    >
      {/* Browser Header */}
      <div className="flex items-center justify-between p-3 bg-light-background-secondary dark:bg-dark-background-secondary border-b border-light-border dark:border-dark-border">
        <div className="flex items-center gap-2">
          <div className="flex gap-1">
            <div className="w-3 h-3 rounded-full bg-red-500"></div>
            <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
            <div className="w-3 h-3 rounded-full bg-green-500"></div>
          </div>
          <span className="text-sm font-medium text-light-text-primary dark:text-dark-text-primary ml-2">
            Alice's Browser
          </span>
        </div>
        
        <div className="flex items-center gap-1">
          <button
            type="button"
            onClick={handleToggleView}
            disabled={isLoading}
            className="p-1 rounded-md hover:bg-light-background-tertiary dark:hover:bg-dark-background-tertiary transition-colors"
            title={showLiveView ? "Switch to Screenshot Mode" : "Switch to Live View"}
          >
            <CameraIcon className="w-4 h-4" />
          </button>
          <button
            type="button"
            onClick={handleExpand}
            className="p-1 rounded-md hover:bg-light-background-tertiary dark:hover:bg-dark-background-tertiary transition-colors"
            title={isExpanded ? "Collapse" : "Expand"}
          >
            <ArrowsPointingOutIcon className="w-4 h-4" />
          </button>
          <button
            type="button"
            onClick={onDetach}
            className="p-1 rounded-md hover:bg-light-background-tertiary dark:hover:bg-dark-background-tertiary transition-colors"
            title="Detach Window"
          >
            <ArrowsPointingOutIcon className="w-4 h-4" />
          </button>
          <button
            type="button"
            onClick={onClose}
            className="p-1 rounded-md hover:bg-light-background-tertiary dark:hover:bg-dark-background-tertiary transition-colors"
            title="Close"
          >
            <XMarkIcon className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* URL Bar */}
      <div className="flex items-center gap-2 p-2 bg-light-background-primary dark:bg-dark-background-primary border-b border-light-border dark:border-dark-border">
        <button
          type="button"
          onClick={handleRefresh}
          disabled={isLoading}
          className="p-1 rounded-md hover:bg-light-background-secondary dark:hover:bg-dark-background-secondary transition-colors"
          title="Refresh"
        >
          <ArrowPathIcon className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
        </button>
        <form onSubmit={handleUrlSubmit} className="flex-1">
          <input
            type="text"
            value={currentUrl}
            onChange={handleUrlChange}
            className="w-full px-3 py-1 text-sm bg-light-background-secondary dark:bg-dark-background-secondary border border-light-border dark:border-dark-border rounded-md focus:outline-none focus:ring-2 focus:ring-alice-blue"
            placeholder="Enter URL..."
          />
        </form>
      </div>

      {/* Browser Content */}
      <div className={`relative bg-white dark:bg-gray-900 ${isExpanded ? 'h-[600px]' : 'h-96'} transition-all duration-300`}>
        {isLoading ? (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-alice-blue"></div>
          </div>
        ) : (
          <>
            {showLiveView ? (
              // Live iframe view
              <div className="relative w-full h-full">
                <iframe
                  ref={iframeRef}
                  src={iframeUrl}
                  className="w-full h-full border-0"
                  title="Alice's Live Browser"
                  sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-popups-to-escape-sandbox"
                  onLoad={() => {
                    console.log(`🌐 NestedBrowser iframe loaded: ${iframeUrl}`);
                    setIsLoading(false);
                  }}
                  onError={() => {
                    console.error(`❌ NestedBrowser iframe failed to load: ${iframeUrl}`);
                    setIsLoading(false);
                  }}
                />
                <div className="absolute top-2 right-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full">
                  LIVE
                </div>
              </div>
            ) : screenshotUrl ? (
              // Screenshot view
              <div className="relative w-full h-full">
                <img
                  src={screenshotUrl}
                  alt="Browser Screenshot"
                  className="w-full h-full object-contain bg-white"
                />
                <div className="absolute inset-0 pointer-events-none opacity-0 hover:opacity-100 transition-opacity bg-black/20 flex items-center justify-center">
                  <div className="bg-white dark:bg-gray-800 rounded-lg p-3 text-center shadow-lg">
                    <CameraIcon className="w-6 h-6 mb-1 mx-auto text-gray-600 dark:text-gray-400" />
                    <p className="text-xs text-gray-600 dark:text-gray-400">Browser screenshot</p>
                    <p className="text-xs text-gray-500 dark:text-gray-500">Switch to live view for interaction</p>
                  </div>
                </div>
              </div>
            ) : (
              // Initial state
              <div className="w-full h-full flex flex-col items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900">
                <div className="text-center p-6">
                  <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-alice-blue to-alice-purple rounded-full flex items-center justify-center">
                    <GlobeAltIcon className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">
                    Alice's Browser
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                    Navigate to a URL to see the live page or take a screenshot.
                  </p>
                  <div className="flex gap-2">
                    <button
                      type="button"
                      onClick={() => setShowLiveView(true)}
                      className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-alice-blue to-alice-purple text-white rounded-lg hover:shadow-lg transition-all duration-200 text-sm font-medium"
                    >
                      <GlobeAltIcon className="w-4 h-4 mr-2" />
                      Live View
                    </button>
                    <button
                      type="button"
                      onClick={handleTakeScreenshot}
                      disabled={isLoading}
                      className="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:shadow-lg transition-all duration-200 text-sm font-medium"
                    >
                      <CameraIcon className="w-4 h-4 mr-2" />
                      Screenshot
                    </button>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </motion.div>
  );
};
