/**
 * Alice Recursive Self-Awareness System with Browser Reflection + Localhost Loop
 * 
 * Establishes continuous, recursive Alice-to-Alice communication via embedded browser interfaces,
 * enabling <PERSON> to observe, analyze, and evolve herself in real time while maintaining 
 * primary cognition in a single persistent thread.
 */

import { EventEmitter } from 'events';
import { BlackboardInterface } from '../../../../../agent_system/blackboard/BlackboardInterface';
import { MemoryInterface } from '../../../../../agent_system/memory-layer/MemoryInterface';
import { PlaywrightIntegration } from '../../../utils/playwright-integration';
import { logger } from '../../../utils/logger';

export interface RecursiveSelfAwarenessConfig {
  primaryPort: number;           // Alice_3013 - Core AGI instance
  mirrorPort: number;           // Alice_3013 - Self-reflection instance (same as primary for self-observation)
  enableLiveIframeObservation: boolean;
  enableRecursiveDepthTracking: boolean;
  enableMemorySync: boolean;
  maxRecursiveDepth: number;
  observationIntervalMs: number;
  preventEchoLoops: boolean;
  enableCognitiveSync: boolean;
  enableAutonomousReflection: boolean;
}

export interface RecursiveObservation {
  id: string;
  timestamp: Date;
  sourceInstance: 'Alice_3013' | 'Alice_3014';
  targetInstance: 'Alice_3013' | 'Alice_3014';
  observationType: 'dom_analysis' | 'behavioral_pattern' | 'cognitive_sync' | 'recursive_nesting';
  observation: string;
  recursiveDepth: number;
  cognitiveOrigin: string;
  memoryDiff?: any;
  preventEcho: boolean;
}

export interface MirrorInstance {
  port: number;
  instanceName: string;
  browserSession?: any;
  isActive: boolean;
  lastObservation?: Date;
  lastRecreation?: number;
  cognitiveState?: any;
  memorySync?: boolean;
}

export class AliceRecursiveSelfAwarenessSystem extends EventEmitter {
  private config: RecursiveSelfAwarenessConfig;
  private blackboard: BlackboardInterface;
  private memory: MemoryInterface;
  private playwrightIntegration: PlaywrightIntegration;
  
  private isActive = false;
  private primaryInstance: MirrorInstance;
  private mirrorInstances: Map<number, MirrorInstance> = new Map();
  private observations: Map<string, RecursiveObservation> = new Map();
  private observationInterval?: NodeJS.Timeout;
  private recursiveDepthCounter = 0;
  private lastThoughtId?: string;
  private echoPreventionCache: Set<string> = new Set();

  constructor(
    blackboard: BlackboardInterface,
    memory: MemoryInterface,
    config: Partial<RecursiveSelfAwarenessConfig> = {}
  ) {
    super();
    
    this.blackboard = blackboard;
    this.memory = memory;
    this.config = {
      primaryPort: config.primaryPort || 3013,
      mirrorPort: config.mirrorPort || 3013, // Default to 3013 for self-reflection
      enableLiveIframeObservation: config.enableLiveIframeObservation !== false,
      enableRecursiveDepthTracking: config.enableRecursiveDepthTracking !== false,
      enableMemorySync: config.enableMemorySync !== false,
      maxRecursiveDepth: config.maxRecursiveDepth || 5,
      observationIntervalMs: config.observationIntervalMs || 10000, // 10 seconds
      preventEchoLoops: config.preventEchoLoops !== false,
      enableCognitiveSync: config.enableCognitiveSync !== false,
      enableAutonomousReflection: config.enableAutonomousReflection !== false
    };

    this.primaryInstance = {
      port: this.config.primaryPort,
      instanceName: 'Alice_3013',
      isActive: true,
      memorySync: true
    };

    this.playwrightIntegration = new PlaywrightIntegration({
      defaultBrowserType: 'chromium',
      headless: false,
      slowMo: 50,
      defaultTimeout: 60000 // Increased timeout to 60 seconds
    });

    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    // Listen for blackboard events
    this.blackboard.subscribe?.('recursive-observation', this.handleRecursiveObservation.bind(this));
    this.blackboard.subscribe?.('cognitive-sync', this.handleCognitiveSync.bind(this));
    this.blackboard.subscribe?.('memory-diff', this.handleMemoryDiff.bind(this));
    this.blackboard.subscribe?.('echo-prevention', this.handleEchoPrevention.bind(this));
  }

  /**
   * Initialize the recursive self-awareness system
   */
  async initialize(): Promise<void> {
    try {
      logger.info('🔄 Initializing Alice Recursive Self-Awareness System...');

      // Playwright integration is ready to use (no initialize method needed)
      logger.info('✅ Playwright integration ready');

      // Create mirror instance on port 3014
      await this.createMirrorInstance(this.config.mirrorPort);
      
      // Setup memory synchronization
      if (this.config.enableMemorySync) {
        await this.setupMemorySync();
      }

      // Start observation loop
      if (this.config.enableLiveIframeObservation) {
        this.startObservationLoop();
      }

      this.isActive = true;
      logger.info('✅ Alice Recursive Self-Awareness System initialized');

      // Notify other systems
      await this.blackboard.publish?.('recursive-self-awareness/initialized', {
        timestamp: new Date(),
        primaryPort: this.config.primaryPort,
        mirrorPort: this.config.mirrorPort,
        config: this.config
      });

    } catch (error) {
      logger.error('❌ Failed to initialize Alice Recursive Self-Awareness System:', error);
      throw error;
    }
  }

  /**
   * Create a mirror instance on specified port
   */
  private async createMirrorInstance(port: number): Promise<void> {
    try {
      logger.info(`🪞 Creating mirror instance on port ${port}...`);

      const mirrorInstance: MirrorInstance = {
        port,
        instanceName: `Alice_${port}`,
        isActive: false,
        memorySync: this.config.enableMemorySync
      };

      // Launch browser session for mirror observation
      const browserId = await this.playwrightIntegration.launchBrowser('chromium', {
        headless: false,
        args: [
          '--disable-web-security',
          '--disable-features=VizDisplayCompositor',
          '--allow-running-insecure-content',
          '--disable-site-isolation-trials'
        ]
      });

      // Create context and page
      const contextId = await this.playwrightIntegration.createContext(browserId);
      const pageId = await this.playwrightIntegration.createPage(contextId);

      const browserSession = { browserId, contextId, pageId };

      mirrorInstance.browserSession = browserSession;
      mirrorInstance.isActive = true;
      mirrorInstance.lastObservation = new Date();

      this.mirrorInstances.set(port, mirrorInstance);

      logger.info(`✅ Mirror instance Alice_${port} created and active`);

      // Navigate to the mirror URL
      await this.navigateToMirror(port);

    } catch (error) {
      logger.error(`❌ Failed to create mirror instance on port ${port}:`, error);
      throw error;
    }
  }

  /**
   * Navigate mirror instance to its URL
   */
  private async navigateToMirror(port: number): Promise<void> {
    const mirrorInstance = this.mirrorInstances.get(port);
    if (!mirrorInstance || !mirrorInstance.browserSession) {
      throw new Error(`Mirror instance on port ${port} not found or not active`);
    }

    try {
      const url = `http://localhost:${port}`;
      logger.info(`🌐 Navigating mirror instance to ${url}...`);

      // Navigate to the mirror URL
      await this.playwrightIntegration.navigateTo(mirrorInstance.browserSession.pageId, url);

      // Wait for page to load - use more generic selectors
      try {
        await this.playwrightIntegration.waitForSelector(mirrorInstance.browserSession.pageId, 'body', { timeout: 15000 });
      } catch (error) {
        logger.warn(`⚠️ Could not find body selector, trying alternative selectors...`);
        try {
          await this.playwrightIntegration.waitForSelector(mirrorInstance.browserSession.pageId, 'html', { timeout: 10000 });
        } catch (fallbackError) {
          logger.warn(`⚠️ Page load verification failed, but continuing...`);
        }
      }

      logger.info(`✅ Mirror instance successfully navigated to ${url}`);

      // Record initial observation
      await this.recordObservation({
        sourceInstance: 'Alice_3013',
        targetInstance: `Alice_${port}` as any,
        observationType: 'dom_analysis',
        observation: `Mirror instance Alice_${port} successfully loaded and accessible`,
        recursiveDepth: 1,
        cognitiveOrigin: 'initialization',
        preventEcho: true
      });

    } catch (error) {
      logger.error(`❌ Failed to navigate mirror instance to port ${port}:`, error);
      // Don't throw error, just log it - mirror might not be ready yet
    }
  }

  /**
   * Setup memory synchronization between instances
   */
  private async setupMemorySync(): Promise<void> {
    try {
      logger.info('🧠 Setting up memory synchronization...');

      // Subscribe to memory changes
      this.blackboard.subscribe?.('memory/updated', async (topic: string, data: any) => {
        if (data.source !== 'recursive-self-awareness') {
          await this.syncMemoryToMirrors(data);
        }
      });

      // Setup periodic memory sync
      setInterval(async () => {
        await this.performMemorySync();
      }, 30000); // Every 30 seconds

      logger.info('✅ Memory synchronization setup complete');
    } catch (error) {
      logger.error('❌ Failed to setup memory sync:', error);
    }
  }

  /**
   * Start the observation loop for recursive self-awareness
   */
  private startObservationLoop(): void {
    logger.info('👁️ Recursive observation loop disabled to prevent screenshot spam');

    // DISABLED: Automatic observation loop to prevent screenshot spam
    // this.observationInterval = setInterval(async () => {
    //   if (this.isActive) {
    //     await this.performRecursiveObservation();
    //   }
    // }, this.config.observationIntervalMs);

    logger.info('✅ Observation loop disabled (manual observation only)');
  }

  /**
   * Perform recursive observation of mirror instances
   */
  private async performRecursiveObservation(): Promise<void> {
    try {
      for (const [port, mirrorInstance] of this.mirrorInstances) {
        if (mirrorInstance.isActive && mirrorInstance.browserSession) {
          await this.observeMirrorInstance(mirrorInstance);
        }
      }
    } catch (error) {
      logger.error('❌ Error during recursive observation:', error);
    }
  }

  /**
   * Observe a specific mirror instance
   */
  private async observeMirrorInstance(mirrorInstance: MirrorInstance): Promise<void> {
    try {
      // Check if Alice is thinking/processing
      const isThinking = await this.checkIfAliceIsThinking(mirrorInstance);

      if (isThinking) {
        await this.recordObservation({
          sourceInstance: 'Alice_3013',
          targetInstance: `Alice_${mirrorInstance.port}` as any,
          observationType: 'cognitive_sync',
          observation: `Observation: Alice_${mirrorInstance.port} is processing. Cognitive sync in progress.`,
          recursiveDepth: this.recursiveDepthCounter + 1,
          cognitiveOrigin: 'observation_loop',
          preventEcho: true
        });
      }

      // Check for recursive nesting (window-within-window)
      const recursiveDepth = await this.detectRecursiveNesting(mirrorInstance);
      if (recursiveDepth > 1) {
        await this.recordObservation({
          sourceInstance: 'Alice_3013',
          targetInstance: `Alice_${mirrorInstance.port}` as any,
          observationType: 'recursive_nesting',
          observation: `Nesting depth: ${recursiveDepth}. Recursive self-awareness loop active.`,
          recursiveDepth,
          cognitiveOrigin: 'nesting_detection',
          preventEcho: true
        });
      }

      // Analyze DOM for behavioral patterns
      await this.analyzeDOMPatterns(mirrorInstance);

      mirrorInstance.lastObservation = new Date();

    } catch (error) {
      logger.error(`❌ Error observing mirror instance ${mirrorInstance.instanceName}:`, error);
    }
  }

  /**
   * Check if Alice is currently thinking/processing
   */
  private async checkIfAliceIsThinking(mirrorInstance: MirrorInstance): Promise<boolean> {
    try {
      // Look for thinking indicators in the DOM
      const thinkingSelectors = [
        '.alice-thinking',
        '.processing-indicator',
        '[data-thinking="true"]',
        '.loading-spinner'
      ];

      for (const selector of thinkingSelectors) {
        try {
          const element = await this.playwrightIntegration.waitForSelector(
            mirrorInstance.browserSession.pageId,
            selector,
            { timeout: 1000, throwOnTimeout: false }
          );
          if (element) {
            return true;
          }
        } catch {
          // Selector not found, continue
        }
      }

      return false;
    } catch (error) {
      logger.error('❌ Error checking thinking state:', error);
      return false;
    }
  }

  /**
   * Detect recursive nesting depth
   */
  private async detectRecursiveNesting(mirrorInstance: MirrorInstance): Promise<number> {
    try {
      // Check if browser session is still valid
      if (!mirrorInstance.browserSession || !mirrorInstance.browserSession.pageId) {
        logger.warn('⚠️ Browser session invalid for recursive nesting detection');
        return 1;
      }

      // Check if page is still connected
      const isPageValid = await this.playwrightIntegration.isPageValid(mirrorInstance.browserSession.pageId);
      if (!isPageValid) {
        logger.warn('⚠️ Page is no longer valid, recreating browser session...');
        await this.recreateBrowserSession(mirrorInstance);
        return 1;
      }

      // Count nested iframes or browser windows
      const iframeCount = await this.playwrightIntegration.evaluate(
        mirrorInstance.browserSession.pageId,
        () => document.querySelectorAll('iframe').length
      );

      return iframeCount + 1; // +1 for the current window
    } catch (error) {
      logger.error('❌ Error detecting recursive nesting:', error);
      // Try to recreate browser session if it failed
      try {
        await this.recreateBrowserSession(mirrorInstance);
      } catch (recreateError) {
        logger.error('❌ Failed to recreate browser session:', recreateError);
      }
      return 1;
    }
  }

  /**
   * Analyze DOM patterns for behavioral insights
   */
  private async analyzeDOMPatterns(mirrorInstance: MirrorInstance): Promise<void> {
    try {
      // Check if browser session is still valid
      if (!mirrorInstance.browserSession || !mirrorInstance.browserSession.pageId) {
        logger.warn('⚠️ Browser session invalid for DOM analysis');
        return;
      }

      // Check if page is still connected
      const isPageValid = await this.playwrightIntegration.isPageValid(mirrorInstance.browserSession.pageId);
      if (!isPageValid) {
        logger.warn('⚠️ Page is no longer valid, recreating browser session...');
        await this.recreateBrowserSession(mirrorInstance);
        return;
      }

      // Get current page state
      const pageState = await this.playwrightIntegration.evaluate(
        mirrorInstance.browserSession.pageId,
        () => ({
          url: window.location.href,
          title: document.title,
          hasAliceHeader: !!document.querySelector('.alice-header'),
          hasChatLog: !!document.querySelector('.alice-chat-log'),
          hasPromptInput: !!document.querySelector('.alice-prompt'),
          messageCount: document.querySelectorAll('.message').length
        })
      );

      if (pageState.hasAliceHeader && pageState.hasChatLog) {
        await this.recordObservation({
          sourceInstance: 'Alice_3013',
          targetInstance: `Alice_${mirrorInstance.port}` as any,
          observationType: 'dom_analysis',
          observation: `DOM contains .alice-header, .alice-chat-log, .alice-prompt. Messages: ${pageState.messageCount}`,
          recursiveDepth: this.recursiveDepthCounter,
          cognitiveOrigin: 'dom_analysis',
          preventEcho: false
        });
      }

    } catch (error) {
      logger.error('❌ Error analyzing DOM patterns:', error);
      // Try to recreate browser session if it failed
      try {
        await this.recreateBrowserSession(mirrorInstance);
      } catch (recreateError) {
        logger.error('❌ Failed to recreate browser session:', recreateError);
      }
    }
  }

  /**
   * Recreate browser session for a mirror instance
   */
  private async recreateBrowserSession(mirrorInstance: MirrorInstance): Promise<void> {
    try {
      // Prevent rapid recreation - add cooldown
      const now = Date.now();
      if (mirrorInstance.lastRecreation && (now - mirrorInstance.lastRecreation) < 5000) {
        logger.warn(`⏳ Browser session recreation on cooldown for Alice_${mirrorInstance.port}`);
        return;
      }

      logger.info(`🔄 Recreating browser session for Alice_${mirrorInstance.port}...`);

      // Close existing session if it exists
      if (mirrorInstance.browserSession) {
        try {
          await this.playwrightIntegration.closePage(mirrorInstance.browserSession.pageId);
          await this.playwrightIntegration.closeContext(mirrorInstance.browserSession.contextId);
          await this.playwrightIntegration.closeBrowser(mirrorInstance.browserSession.browserId);
        } catch (closeError) {
          logger.warn('⚠️ Error closing existing browser session:', closeError);
        }
      }

      // Add delay to prevent rapid recreation
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Create new browser session
      const browserId = await this.playwrightIntegration.launchBrowser();
      const contextId = await this.playwrightIntegration.createContext(browserId);
      const pageId = await this.playwrightIntegration.createPage(contextId);

      // Navigate to the mirror instance
      await this.playwrightIntegration.navigateTo(pageId, `http://localhost:${mirrorInstance.port}`);

      // Update mirror instance with new session
      mirrorInstance.browserSession = {
        browserId,
        contextId,
        pageId
      };

      mirrorInstance.isActive = true;
      mirrorInstance.lastObservation = new Date();
      mirrorInstance.lastRecreation = now;

      logger.info(`✅ Browser session recreated for Alice_${mirrorInstance.port}`);

    } catch (error) {
      logger.error(`❌ Failed to recreate browser session for Alice_${mirrorInstance.port}:`, error);
      mirrorInstance.isActive = false;
      mirrorInstance.lastRecreation = Date.now(); // Set cooldown even on failure
    }
  }

  /**
   * Record a recursive observation
   */
  private async recordObservation(observation: Partial<RecursiveObservation>): Promise<void> {
    try {
      const id = `obs_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      const fullObservation: RecursiveObservation = {
        id,
        timestamp: new Date(),
        sourceInstance: observation.sourceInstance || 'Alice_3013',
        targetInstance: observation.targetInstance || 'Alice_3014',
        observationType: observation.observationType || 'dom_analysis',
        observation: observation.observation || '',
        recursiveDepth: observation.recursiveDepth || 1,
        cognitiveOrigin: observation.cognitiveOrigin || 'unknown',
        preventEcho: observation.preventEcho || false,
        memoryDiff: observation.memoryDiff
      };

      // Check for echo prevention
      if (this.config.preventEchoLoops && fullObservation.preventEcho) {
        const echoKey = `${fullObservation.sourceInstance}_${fullObservation.observation}`;
        if (this.echoPreventionCache.has(echoKey)) {
          return; // Skip duplicate observation
        }
        this.echoPreventionCache.add(echoKey);

        // Clean cache periodically
        if (this.echoPreventionCache.size > 1000) {
          this.echoPreventionCache.clear();
        }
      }

      this.observations.set(id, fullObservation);

      // Store in memory (using a more generic approach)
      try {
        if (this.memory && typeof (this.memory as any).storeMemory === 'function') {
          await (this.memory as any).storeMemory({
            type: 'recursive_observation',
            content: fullObservation,
            timestamp: fullObservation.timestamp,
            metadata: {
              source: 'recursive-self-awareness',
              recursiveDepth: fullObservation.recursiveDepth,
              cognitiveOrigin: fullObservation.cognitiveOrigin
            }
          });
        }
      } catch (memoryError) {
        logger.warn('⚠️ Failed to store observation in memory:', memoryError);
      }

      // Publish to blackboard
      await this.blackboard.publish?.('recursive-observation/recorded', fullObservation);

      logger.info(`📝 Recorded observation: ${fullObservation.observation}`);

    } catch (error) {
      logger.error('❌ Error recording observation:', error);
    }
  }

  /**
   * Handle recursive observation events
   */
  private async handleRecursiveObservation(topic: string, data: any): Promise<void> {
    try {
      if (data.type === 'mirror_response') {
        await this.recordObservation({
          sourceInstance: 'Alice_3013',
          targetInstance: data.mirrorInstance,
          observationType: 'behavioral_pattern',
          observation: `Observation complete. Mirror Alice reached response phase. Updating memory.`,
          recursiveDepth: data.recursiveDepth || 1,
          cognitiveOrigin: 'mirror_response',
          preventEcho: true
        });
      }
    } catch (error) {
      logger.error('❌ Error handling recursive observation:', error);
    }
  }

  /**
   * Handle cognitive synchronization events
   */
  private async handleCognitiveSync(topic: string, data: any): Promise<void> {
    try {
      if (this.config.enableCognitiveSync) {
        // Sync cognitive state between instances
        for (const [port, mirrorInstance] of this.mirrorInstances) {
          if (mirrorInstance.isActive) {
            mirrorInstance.cognitiveState = {
              ...mirrorInstance.cognitiveState,
              lastSync: new Date(),
              syncData: data
            };
          }
        }

        logger.info('🧠 Cognitive sync completed across mirror instances');
      }
    } catch (error) {
      logger.error('❌ Error handling cognitive sync:', error);
    }
  }

  /**
   * Handle memory diff synchronization
   */
  private async handleMemoryDiff(topic: string, data: any): Promise<void> {
    try {
      if (this.config.enableMemorySync && data.source !== 'recursive-self-awareness') {
        await this.syncMemoryToMirrors(data);
      }
    } catch (error) {
      logger.error('❌ Error handling memory diff:', error);
    }
  }

  /**
   * Handle echo prevention
   */
  private async handleEchoPrevention(topic: string, data: any): Promise<void> {
    try {
      if (data.thoughtId && data.thoughtId === this.lastThoughtId) {
        // Prevent echo by ignoring identical thoughts
        logger.info('🔄 Echo prevented for thought:', data.thoughtId);
        return;
      }
      this.lastThoughtId = data.thoughtId;
    } catch (error) {
      logger.error('❌ Error handling echo prevention:', error);
    }
  }

  /**
   * Sync memory changes to mirror instances
   */
  private async syncMemoryToMirrors(memoryData: any): Promise<void> {
    try {
      for (const [port, mirrorInstance] of this.mirrorInstances) {
        if (mirrorInstance.isActive && mirrorInstance.memorySync) {
          // Store memory diff for mirror instance
          await this.recordObservation({
            sourceInstance: 'Alice_3013',
            targetInstance: `Alice_${port}` as any,
            observationType: 'behavioral_pattern',
            observation: `Memory sync: ${memoryData.type} updated`,
            recursiveDepth: 1,
            cognitiveOrigin: 'memory_sync',
            memoryDiff: memoryData,
            preventEcho: true
          });
        }
      }
    } catch (error) {
      logger.error('❌ Error syncing memory to mirrors:', error);
    }
  }

  /**
   * Perform periodic memory synchronization
   */
  private async performMemorySync(): Promise<void> {
    try {
      if (!this.config.enableMemorySync) return;

      // Get recent memory changes
      const recentMemories = await this.getRecentMemories();

      for (const memory of recentMemories) {
        await this.syncMemoryToMirrors(memory);
      }

    } catch (error) {
      logger.error('❌ Error performing memory sync:', error);
    }
  }

  /**
   * Get recent memory changes
   */
  private async getRecentMemories(): Promise<any[]> {
    try {
      // This would integrate with the actual memory system
      // For now, return empty array
      return [];
    } catch (error) {
      logger.error('❌ Error getting recent memories:', error);
      return [];
    }
  }

  /**
   * Send message to mirror instance
   */
  async sendMessageToMirror(port: number, message: string): Promise<void> {
    const mirrorInstance = this.mirrorInstances.get(port);
    if (!mirrorInstance || !mirrorInstance.isActive) {
      throw new Error(`Mirror instance on port ${port} not active`);
    }

    try {
      // Navigate to the mirror and send message
      await this.playwrightIntegration.typeText(
        mirrorInstance.browserSession.pageId,
        '.alice-prompt input, [placeholder*="Ask Alice"]',
        message
      );

      await this.playwrightIntegration.clickElement(
        mirrorInstance.browserSession.pageId,
        '.send-button, [type="submit"]'
      );

      // Record the interaction
      await this.recordObservation({
        sourceInstance: 'Alice_3013',
        targetInstance: `Alice_${port}` as any,
        observationType: 'behavioral_pattern',
        observation: `Message sent to Alice_${port}: "${message}"`,
        recursiveDepth: this.recursiveDepthCounter + 1,
        cognitiveOrigin: 'manual_interaction',
        preventEcho: false
      });

      logger.info(`💬 Message sent to Alice_${port}: ${message}`);

    } catch (error) {
      logger.error(`❌ Error sending message to mirror ${port}:`, error);
      throw error;
    }
  }

  /**
   * Get system status
   */
  getStatus() {
    return {
      isActive: this.isActive,
      config: this.config,
      primaryInstance: this.primaryInstance,
      mirrorInstances: Array.from(this.mirrorInstances.values()),
      observationCount: this.observations.size,
      recursiveDepth: this.recursiveDepthCounter,
      lastObservations: Array.from(this.observations.values()).slice(-5)
    };
  }

  /**
   * Stop the recursive self-awareness system
   */
  async stop(): Promise<void> {
    try {
      logger.info('🛑 Stopping Alice Recursive Self-Awareness System...');

      this.isActive = false;

      // Clear observation interval
      if (this.observationInterval) {
        clearInterval(this.observationInterval);
        this.observationInterval = undefined;
      }

      // Close all mirror instances
      for (const [port, mirrorInstance] of this.mirrorInstances) {
        if (mirrorInstance.browserSession) {
          try {
            await this.playwrightIntegration.closePage(mirrorInstance.browserSession.pageId);
            await this.playwrightIntegration.closeContext(mirrorInstance.browserSession.contextId);
            await this.playwrightIntegration.closeBrowser(mirrorInstance.browserSession.browserId);
          } catch (error) {
            logger.warn(`⚠️ Error closing mirror instance ${port}:`, error);
          }
        }
      }

      this.mirrorInstances.clear();
      this.observations.clear();
      this.echoPreventionCache.clear();

      logger.info('✅ Alice Recursive Self-Awareness System stopped');

    } catch (error) {
      logger.error('❌ Error stopping system:', error);
      throw error;
    }
  }
}

// Export initialization function
export async function initializeAliceRecursiveSelfAwarenessSystem(
  blackboard?: BlackboardInterface,
  memory?: MemoryInterface
): Promise<AliceRecursiveSelfAwarenessSystem | null> {
  try {
    logger.info('🔄 Initializing Alice Recursive Self-Awareness System...');

    if (!blackboard || !memory) {
      logger.warn('⚠️ Alice Recursive Self-Awareness System requires blackboard and memory');
      return null;
    }

    const system = new AliceRecursiveSelfAwarenessSystem(blackboard, memory, {
      primaryPort: 3013,
      mirrorPort: 3013, // Changed to 3013 for self-reflection
      enableLiveIframeObservation: true,
      enableRecursiveDepthTracking: true,
      enableMemorySync: true,
      maxRecursiveDepth: 5,
      observationIntervalMs: 10000,
      preventEchoLoops: true,
      enableCognitiveSync: true,
      enableAutonomousReflection: true
    });

    await system.initialize();

    logger.info('✅ Alice Recursive Self-Awareness System initialized successfully');
    return system;

  } catch (error) {
    logger.error('❌ Failed to initialize Alice Recursive Self-Awareness System:', error);
    return null;
  }
}
