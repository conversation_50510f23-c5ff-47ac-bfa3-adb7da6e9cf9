﻿/**
 * Alerting System Integration Test
 * 
 * Tests the integration of the alerting system.
 */

import { MetricsAPI } from '../metrics/api';
import { AlertManager } from '../metrics/alerts/AlertManager';
import { AgentType } from '../../../../../agents/agent-base/types';

describe('Alerting System Integration', () => {
  let metricsAPI: MetricsAPI;
  
  beforeEach(() => {
    // Create a fresh instance for each test
    metricsAPI = new MetricsAPI();
    
    // Mock console methods
    jest.spyOn(console, 'log').mockImplementation(() => {});
    jest.spyOn(console, 'warn').mockImplementation(() => {});
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });
  
  afterEach(() => {
    // Clean up any test data
    jest.restoreAllMocks();
  });
  
  test('should add and retrieve alert rules', () => {
    // Add an alert rule
    const newRule = metricsAPI.addAlertRule({
      name: 'Test Alert Rule',
      description: 'Test alert rule for unit testing',
      metricName: 'success_rate',
      condition: 'below',
      threshold: 0.7,
      duration: 300000, // 5 minutes
      severity: 'warning',
      enabled: true,
      channels: ['console', 'in-app']
    });
    
    // Verify the rule was added
    expect(newRule).toBeDefined();
    expect(newRule.id).toBeDefined();
    expect(newRule.name).toBe('Test Alert Rule');
    
    // Get all alert rules
    const rules = metricsAPI.getAlertRules();
    
    // Verify the rule is in the list
    expect(rules.length).toBeGreaterThan(0);
    const addedRule = rules.find(r => r.id === newRule.id);
    expect(addedRule).toBeDefined();
    expect(addedRule?.name).toBe('Test Alert Rule');
    
    // Update the rule
    const updated = metricsAPI.updateAlertRule(newRule.id, {
      threshold: 0.6,
      severity: 'critical'
    });
    
    // Verify the rule was updated
    expect(updated).toBe(true);
    
    // Get all alert rules again
    const updatedRules = metricsAPI.getAlertRules();
    
    // Verify the rule was updated
    const updatedRule = updatedRules.find(r => r.id === newRule.id);
    expect(updatedRule).toBeDefined();
    expect(updatedRule?.threshold).toBeCloseTo(0.6);
    expect(updatedRule?.severity).toBe('critical');
    
    // Delete the rule
    const deleted = metricsAPI.deleteAlertRule(newRule.id);
    
    // Verify the rule was deleted
    expect(deleted).toBe(true);
    
    // Get all alert rules again
    const finalRules = metricsAPI.getAlertRules();
    
    // Verify the rule is no longer in the list
    const deletedRule = finalRules.find(r => r.id === newRule.id);
    expect(deletedRule).toBeUndefined();
  });
  
  test('should trigger alerts based on metrics', () => {
    // Create an AlertManager directly for testing
    const metricsStorage = (metricsAPI as any).metricsAggregator.metricsStorage;
    const alertManager = new AlertManager(metricsStorage);
    
    // Add a test rule
    alertManager.addAlertRule({
      name: 'Low Success Rate',
      description: 'Alert when success rate falls below threshold',
      metricName: 'success_rate',
      condition: 'below',
      threshold: 0.7,
      duration: 0, // No duration for testing
      severity: 'warning',
      enabled: true,
      channels: ['console']
    });
    
    // Start monitoring
    alertManager.startMonitoring();
    
    // Record a metric that should trigger the alert
    const agentId = 'test-agent-1';
    const agentType = 'EnhancedTestAgent' as AgentType;
    
    metricsStorage.storeMetric(agentId, agentType, {
      name: 'success_rate',
      value: 0.5, // Below threshold
      description: 'Success rate of operations',
      metricType: 'ratio',
      timestamp: Date.now()
    });
    
    // Manually check alert rules
    alertManager['checkAlertRules']();
    
    // Get active alerts
    const activeAlerts = alertManager.getActiveAlerts();
    
    // Verify an alert was triggered
    expect(activeAlerts.length).toBeGreaterThan(0);
    const successRateAlert = activeAlerts.find(a => a.metricName === 'success_rate');
    expect(successRateAlert).toBeDefined();
    expect(successRateAlert?.value).toBeCloseTo(0.5);
    expect(successRateAlert?.threshold).toBeCloseTo(0.7);
    expect(successRateAlert?.condition).toBe('below');
    expect(successRateAlert?.severity).toBe('warning');
    expect(successRateAlert?.status).toBe('active');
    
    // Record a metric that should resolve the alert
    metricsStorage.storeMetric(agentId, agentType, {
      name: 'success_rate',
      value: 0.8, // Above threshold
      description: 'Success rate of operations',
      metricType: 'ratio',
      timestamp: Date.now()
    });
    
    // Manually check alert rules again
    alertManager['checkAlertRules']();
    
    // Get active alerts again
    const updatedActiveAlerts = alertManager.getActiveAlerts();
    
    // Verify the alert was resolved
    expect(updatedActiveAlerts.length).toBe(0);
    
    // Get alert history
    const alertHistory = alertManager.getAlertHistory();
    
    // Verify the alert is in the history
    expect(alertHistory.length).toBeGreaterThan(0);
    const historicalAlert = alertHistory.find(a => a.metricName === 'success_rate');
    expect(historicalAlert).toBeDefined();
    expect(historicalAlert?.status).toBe('resolved');
    expect(historicalAlert?.resolvedAt).toBeDefined();
    
    // Stop monitoring
    alertManager.stopMonitoring();
  });
  
  test('should acknowledge alerts', () => {
    // Create an AlertManager directly for testing
    const metricsStorage = (metricsAPI as any).metricsAggregator.metricsStorage;
    const alertManager = new AlertManager(metricsStorage);
    
    // Add a test rule
    alertManager.addAlertRule({
      name: 'High Response Time',
      description: 'Alert when response time exceeds threshold',
      metricName: 'response_time',
      condition: 'above',
      threshold: 1000,
      duration: 0, // No duration for testing
      severity: 'critical',
      enabled: true,
      channels: ['console']
    });
    
    // Record a metric that should trigger the alert
    const agentId = 'test-agent-2';
    const agentType = 'EnhancedTestAgent' as AgentType;
    
    metricsStorage.storeMetric(agentId, agentType, {
      name: 'response_time',
      value: 2000, // Above threshold
      description: 'Response time for operations',
      metricType: 'time',
      timestamp: Date.now()
    });
    
    // Manually check alert rules
    alertManager['checkAlertRules']();
    
    // Get active alerts
    const activeAlerts = alertManager.getActiveAlerts();
    
    // Verify an alert was triggered
    expect(activeAlerts.length).toBeGreaterThan(0);
    const responseTimeAlert = activeAlerts.find(a => a.metricName === 'response_time');
    expect(responseTimeAlert).toBeDefined();
    
    // Acknowledge the alert
    const acknowledged = alertManager.acknowledgeAlert(responseTimeAlert!.id, 'test-user');
    
    // Verify the alert was acknowledged
    expect(acknowledged).toBe(true);
    
    // Get active alerts again
    const updatedActiveAlerts = alertManager.getActiveAlerts();
    
    // Verify the alert is still active but acknowledged
    expect(updatedActiveAlerts.length).toBeGreaterThan(0);
    const updatedAlert = updatedActiveAlerts.find(a => a.id === responseTimeAlert!.id);
    expect(updatedAlert).toBeDefined();
    expect(updatedAlert?.status).toBe('acknowledged');
    expect(updatedAlert?.acknowledgedAt).toBeDefined();
    expect(updatedAlert?.acknowledgedBy).toBe('test-user');
  });
  
  test('should get alerts for a specific agent', () => {
    // Create an AlertManager directly for testing
    const metricsStorage = (metricsAPI as any).metricsAggregator.metricsStorage;
    const alertManager = new AlertManager(metricsStorage);
    
    // Add a test rule
    alertManager.addAlertRule({
      name: 'Low Overall Performance',
      description: 'Alert when overall performance falls below threshold',
      metricName: 'overall_performance',
      condition: 'below',
      threshold: 0.7,
      duration: 0, // No duration for testing
      severity: 'warning',
      enabled: true,
      channels: ['console']
    });
    
    // Record metrics for multiple agents
    const agentId1 = 'test-agent-3';
    const agentId2 = 'test-agent-4';
    const agentType = 'EnhancedTestAgent' as AgentType;
    
    // Agent 1 - below threshold
    metricsStorage.storeMetric(agentId1, agentType, {
      name: 'overall_performance',
      value: 0.5, // Below threshold
      description: 'Overall agent performance',
      metricType: 'ratio',
      timestamp: Date.now()
    });
    
    // Agent 2 - above threshold
    metricsStorage.storeMetric(agentId2, agentType, {
      name: 'overall_performance',
      value: 0.8, // Above threshold
      description: 'Overall agent performance',
      metricType: 'ratio',
      timestamp: Date.now()
    });
    
    // Manually check alert rules
    alertManager['checkAlertRules']();
    
    // Get alerts for agent 1
    const agent1Alerts = alertManager.getAlertsForAgent(agentId1);
    
    // Verify agent 1 has an alert
    expect(agent1Alerts.length).toBeGreaterThan(0);
    expect(agent1Alerts[0].metricName).toBe('overall_performance');
    
    // Get alerts for agent 2
    const agent2Alerts = alertManager.getAlertsForAgent(agentId2);
    
    // Verify agent 2 has no alerts
    expect(agent2Alerts.length).toBe(0);
  });
});

