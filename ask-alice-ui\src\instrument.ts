import * as Sentry from '@sentry/react';
import React from 'react';
import {
  useLocation,
  useNavigationType,
  createRoutesFromChildren,
  matchRoutes,
} from 'react-router-dom';

// Initialize Sentry for comprehensive frontend error tracking and performance monitoring
Sentry.init({
  dsn: "https://<EMAIL>/****************",
  integrations: [
    // Browser tracing for performance monitoring
    Sentry.browserTracingIntegration({
      // Capture interactions like clicks, navigation
      enableInp: true,
      // Track Core Web Vitals
      enableLongTask: true,
      // Basic routing instrumentation without React Router specific features
      // routingInstrumentation: Sentry.reactRouterV6Instrumentation(
      //   React.useEffect,
      //   useLocation,
      //   useNavigationType,
      //   createRoutesFromChildren,
      //   matchRoutes
      // ),
    }),

    // Session replay for debugging
    Sentry.replayIntegration({
      // Capture 10% of all sessions, plus 100% of sessions with an error
      sessionSampleRate: 0.1,
      errorSampleRate: 1.0,
      // Mask sensitive data
      maskAllText: false,
      maskAllInputs: true,
      blockAllMedia: false,
    }),

    // React-specific error tracking (commented out if not available)
    // Sentry.reactErrorBoundaryIntegration(),

    // Additional browser integrations (using safe checks)
    ...(Sentry.httpContextIntegration ? [Sentry.httpContextIntegration()] : []),
    ...(Sentry.contextLinesIntegration ? [Sentry.contextLinesIntegration()] : []),
    ...(Sentry.linkedErrorsIntegration ? [Sentry.linkedErrorsIntegration()] : []),
    ...(Sentry.dedupeIntegration ? [Sentry.dedupeIntegration()] : []),
  ],

  // Performance monitoring - capture 100% in development, adjust for production
  tracesSampleRate: import.meta.env.MODE === 'development' ? 1.0 : 0.1,

  // Enable automatic instrumentation of user interactions
  profilesSampleRate: import.meta.env.MODE === 'development' ? 1.0 : 0.1,

  // Set `tracePropagationTargets` to control distributed tracing
  tracePropagationTargets: [
    "localhost",
    /^http:\/\/localhost:8003\/api/,
    /^http:\/\/localhost:3013/,
    /^https:\/\/.*\.alice-agi\.com/,
  ],

  // Session replay configuration
  replaysSessionSampleRate: 0.1,
  replaysOnErrorSampleRate: 1.0,

  // Environment and release information
  environment: import.meta.env.MODE || 'development',
  release: `alice-agi-frontend@${import.meta.env.VITE_APP_VERSION || '1.0.0'}`,

  // Enhanced error processing
  beforeSend(event, hint) {
    // Add comprehensive Alice-specific context
    if (!event.extra) event.extra = {};

    event.extra.aliceSystem = 'Alice AGI Frontend';
    event.extra.timestamp = new Date().toISOString();
    event.extra.userAgent = navigator.userAgent;
    event.extra.url = window.location.href;
    event.extra.referrer = document.referrer;

    // Add browser performance metrics
    if (window.performance) {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      if (navigation) {
        event.extra.performanceMetrics = {
          loadTime: navigation.loadEventEnd - navigation.loadEventStart,
          domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
          firstPaint: performance.getEntriesByName('first-paint')[0]?.startTime,
          firstContentfulPaint: performance.getEntriesByName('first-contentful-paint')[0]?.startTime,
        };
      }
    }

    // Add memory usage if available
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      event.extra.memoryUsage = {
        usedJSHeapSize: memory.usedJSHeapSize,
        totalJSHeapSize: memory.totalJSHeapSize,
        jsHeapSizeLimit: memory.jsHeapSizeLimit,
      };
    }

    // Add React component stack if available
    if (hint.originalException && hint.originalException instanceof Error) {
      const componentStack = hint.originalException.stack;
      if (componentStack && componentStack.includes('react')) {
        event.extra.reactComponentStack = componentStack;
      }
    }

    // Filter out development-only errors in production
    if (import.meta.env.MODE === 'production') {
      if (event.exception?.values?.[0]?.value?.includes('HMR')) {
        return null; // Don't send HMR errors in production
      }
    }

    return event;
  },

  // Enhanced error filtering
  ignoreErrors: [
    // Ignore common non-critical errors
    'Non-Error promise rejection captured',
    'ResizeObserver loop limit exceeded',
    'Script error.',
    'Network request failed',
    'Loading chunk',
    'Loading CSS chunk',

    // Ignore browser extension errors
    /extension\//i,
    /^chrome:\/\//i,
    /^moz-extension:\/\//i,
    /^safari-extension:\/\//i,

    // Ignore common React development errors
    /Warning: /,
    /React DevTools/,

    // Ignore common third-party errors
    /facebook\.net/i,
    /google-analytics/i,
    /googletagmanager/i,
  ],

  // Additional configuration for better debugging
  debug: import.meta.env.MODE === 'development',
  sendDefaultPii: true, // Send personally identifiable information
  attachStacktrace: true, // Attach stack traces to all messages

  // Configure which transactions to capture
  beforeSendTransaction(event) {
    // Add transaction context
    if (!event.extra) event.extra = {};
    event.extra.aliceSystem = 'Alice AGI Frontend';
    event.extra.transactionType = event.transaction;

    return event;
  },
});

export default Sentry;
