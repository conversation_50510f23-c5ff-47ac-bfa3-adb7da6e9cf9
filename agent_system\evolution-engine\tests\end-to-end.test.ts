﻿/**
 * End-to-End Integration Test
 * 
 * Tests the integration of all components of the metrics and evolution systems.
 */

import { MetricsAPI } from '../metrics/api';
import { 
  AutonomousEvolutionManager,
  EvolutionScheduler,
  EvolutionFeedbackLoop
} from '../autonomous';
import { EvolutionEngine, EvolutionResult } from '../';
import { BlackboardInterface } from '@agent_system/blackboard/BlackboardInterface';
import { AgentType } from '../../../../../agents/agent-base/types';

describe('End-to-End Integration', () => {
  let metricsAPI: MetricsAPI;
  let evolutionEngine: EvolutionEngine;
  let blackboard: BlackboardInterface;
  let autonomousEvolutionManager: AutonomousEvolutionManager;
  let evolutionScheduler: EvolutionScheduler;
  let evolutionFeedbackLoop: EvolutionFeedbackLoop;
  
  beforeEach(() => {
    // Create instances
    metricsAPI = new MetricsAPI();
    evolutionEngine = new EvolutionEngine();
    blackboard = new BlackboardInterface();
    
    // Mock evolution engine evolveAgent method
    jest.spyOn(evolutionEngine, 'evolveAgent').mockImplementation(async (plan) => {
      // Simulate a successful evolution
      const result: EvolutionResult = {
        success: true,
        message: 'Evolution completed successfully',
        changes: [
          {
            type: 'parameter',
            name: 'responseThreshold',
            oldValue: '0.5',
            newValue: '0.7'
          }
        ],
        metrics: [
          {
            name: plan.targetMetrics[0].name,
            before: plan.targetMetrics[0].currentValue,
            after: plan.targetMetrics[0].targetValue,
            improvement: plan.targetMetrics[0].targetValue - plan.targetMetrics[0].currentValue
          }
        ]
      };
      
      return result;
    });
    
    // Mock blackboard methods
    jest.spyOn(blackboard, 'publish').mockImplementation((topic, data) => {
      // Simulate publishing to the blackboard
      if (blackboardSubscribers[topic]) {
        blackboardSubscribers[topic].forEach(callback => {
          callback(data);
        });
      }
    });
    
    jest.spyOn(blackboard, 'subscribe').mockImplementation((topic, callback) => {
      // Simulate subscribing to the blackboard
      if (!blackboardSubscribers[topic]) {
        blackboardSubscribers[topic] = [];
      }
      
      blackboardSubscribers[topic].push(callback);
    });
    
    // Create a map to store blackboard subscribers
    const blackboardSubscribers: Record<string, ((data: any) => void)[]> = {};
    
    // Create autonomous evolution components
    autonomousEvolutionManager = new AutonomousEvolutionManager(
      metricsAPI,
      evolutionEngine,
      blackboard
    );
    
    evolutionScheduler = new EvolutionScheduler(
      autonomousEvolutionManager,
      blackboard
    );
    
    evolutionFeedbackLoop = new EvolutionFeedbackLoop(
      blackboard,
      metricsAPI
    );
    
    // Start monitoring
    autonomousEvolutionManager.startMonitoring();
    evolutionScheduler.start();
    
    // Mock console methods
    jest.spyOn(console, 'log').mockImplementation(() => {});
    jest.spyOn(console, 'warn').mockImplementation(() => {});
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });
  
  afterEach(() => {
    // Stop monitoring
    autonomousEvolutionManager.stopMonitoring();
    evolutionScheduler.stop();
    
    // Clean up any test data
    jest.restoreAllMocks();
  });
  
  test('should trigger evolution based on metrics and generate feedback', async () => {
    // Add an alert rule for low success rate
    metricsAPI.addAlertRule({
      name: 'Low Success Rate',
      description: 'Alert when success rate falls below threshold',
      metricName: 'success_rate',
      condition: 'below',
      threshold: 0.7,
      duration: 0, // No duration for testing
      severity: 'warning',
      enabled: true,
      channels: ['console']
    });
    
    // Add an evolution rule for low success rate
    autonomousEvolutionManager.addRule({
      name: 'Low Success Rate Evolution',
      description: 'Evolve agents with low success rate',
      condition: {
        metricName: 'success_rate',
        operator: 'less_than',
        threshold: 0.7,
        duration: 0 // No duration for testing
      },
      agentTypes: [], // All agent types
      priority: 'high',
      cooldownMs: 0, // No cooldown for testing
      enabled: true
    });
    
    // Record a metric that should trigger both the alert and evolution
    const agentId = 'test-agent-1';
    const agentType = 'EnhancedTestAgent' as AgentType;
    
    metricsAPI.recordMetric(agentId, agentType, {
      name: 'success_rate',
      value: 0.5, // Below threshold
      description: 'Success rate of operations',
      metricType: 'ratio',
      timestamp: Date.now()
    });
    
    // Manually trigger the rule check
    await autonomousEvolutionManager['checkEvolutionRules']();
    
    // Verify that evolveAgent was called
    expect(evolutionEngine.evolveAgent).toHaveBeenCalled();
    
    // Verify the evolution plan
    const plan = (evolutionEngine.evolveAgent as jest.Mock).mock.calls[0][0];
    expect(plan.agentId).toBe(agentId);
    expect(plan.agentType).toBe(agentType);
    expect(plan.targetMetrics[0].name).toBe('success_rate');
    
    // Get active alerts
    const activeAlerts = metricsAPI.getActiveAlerts();
    
    // Verify an alert was triggered
    expect(activeAlerts.length).toBeGreaterThan(0);
    const successRateAlert = activeAlerts.find(a => a.metricName === 'success_rate');
    expect(successRateAlert).toBeDefined();
    
    // Record a metric that should resolve the alert
    metricsAPI.recordMetric(agentId, agentType, {
      name: 'success_rate',
      value: 0.9, // Above threshold
      description: 'Success rate of operations',
      metricType: 'ratio',
      timestamp: Date.now()
    });
    
    // Get the evolution history
    const evolutionHistory = autonomousEvolutionManager.getHistory();
    
    // Verify the evolution is in the history
    expect(evolutionHistory.length).toBeGreaterThan(0);
    const agentEvolution = evolutionHistory.find(e => e.agentId === agentId);
    expect(agentEvolution).toBeDefined();
    expect(agentEvolution?.status).toBe('completed');
    expect(agentEvolution?.result).not.toBeNull();
    expect(agentEvolution?.result?.success).toBe(true);
  });
  
  test('should integrate metrics, alerts, and evolution with custom metrics', async () => {
    // Register a custom metric type
    metricsAPI.registerMetricType({
      name: 'profit_margin',
      description: 'Profit margin as a percentage',
      valueType: 'number',
      valueRange: [0, 1],
      defaultAggregation: 'average',
      availableAggregations: ['average', 'min', 'max'],
      formatValue: (value) => `${(value * 100).toFixed(1)}%`,
      validateValue: (value) => value >= 0 && value <= 1
    });
    
    // Add an alert rule for low profit margin
    metricsAPI.addAlertRule({
      name: 'Low Profit Margin',
      description: 'Alert when profit margin falls below threshold',
      metricName: 'profit_margin',
      condition: 'below',
      threshold: 0.2,
      duration: 0, // No duration for testing
      severity: 'critical',
      enabled: true,
      channels: ['console']
    });
    
    // Add an evolution rule for low profit margin
    autonomousEvolutionManager.addRule({
      name: 'Low Profit Margin Evolution',
      description: 'Evolve agents with low profit margin',
      condition: {
        metricName: 'profit_margin',
        operator: 'less_than',
        threshold: 0.2,
        duration: 0 // No duration for testing
      },
      agentTypes: [], // All agent types
      priority: 'critical',
      cooldownMs: 0, // No cooldown for testing
      enabled: true
    });
    
    // Record a metric that should trigger both the alert and evolution
    const agentId = 'test-agent-2';
    const agentType = 'EnhancedTestAgent' as AgentType;
    
    metricsAPI.recordMetric(agentId, agentType, {
      name: 'profit_margin',
      value: 0.15, // Below threshold
      description: 'Profit margin',
      metricType: 'profit_margin',
      timestamp: Date.now()
    });
    
    // Manually trigger the rule check
    await autonomousEvolutionManager['checkEvolutionRules']();
    
    // Verify that evolveAgent was called
    expect(evolutionEngine.evolveAgent).toHaveBeenCalled();
    
    // Get active alerts
    const activeAlerts = metricsAPI.getActiveAlerts();
    
    // Verify an alert was triggered
    expect(activeAlerts.length).toBeGreaterThan(0);
    const profitMarginAlert = activeAlerts.find(a => a.metricName === 'profit_margin');
    expect(profitMarginAlert).toBeDefined();
    expect(profitMarginAlert?.severity).toBe('critical');
    
    // Format the metric value
    const formattedValue = metricsAPI.formatMetricValue('profit_margin', 0.15);
    expect(formattedValue).toBe('15.0%');
  });
  
  test('should handle multiple agents and metrics in a complex scenario', async () => {
    // Add multiple evolution rules
    autonomousEvolutionManager.addRule({
      name: 'Low Success Rate Evolution',
      description: 'Evolve agents with low success rate',
      condition: {
        metricName: 'success_rate',
        operator: 'less_than',
        threshold: 0.7,
        duration: 0 // No duration for testing
      },
      agentTypes: [], // All agent types
      priority: 'medium',
      cooldownMs: 0, // No cooldown for testing
      enabled: true
    });
    
    autonomousEvolutionManager.addRule({
      name: 'High Response Time Evolution',
      description: 'Evolve agents with high response time',
      condition: {
        metricName: 'response_time',
        operator: 'greater_than',
        threshold: 1000,
        duration: 0 // No duration for testing
      },
      agentTypes: [], // All agent types
      priority: 'high',
      cooldownMs: 0, // No cooldown for testing
      enabled: true
    });
    
    // Record metrics for multiple agents
    const agent1Id = 'test-agent-3';
    const agent2Id = 'test-agent-4';
    const agentType = 'EnhancedTestAgent' as AgentType;
    
    // Agent 1 - low success rate
    metricsAPI.recordMetric(agent1Id, agentType, {
      name: 'success_rate',
      value: 0.6, // Below threshold
      description: 'Success rate of operations',
      metricType: 'ratio',
      timestamp: Date.now()
    });
    
    // Agent 1 - normal response time
    metricsAPI.recordMetric(agent1Id, agentType, {
      name: 'response_time',
      value: 500, // Below threshold
      description: 'Response time for operations',
      metricType: 'time',
      timestamp: Date.now()
    });
    
    // Agent 2 - normal success rate
    metricsAPI.recordMetric(agent2Id, agentType, {
      name: 'success_rate',
      value: 0.8, // Above threshold
      description: 'Success rate of operations',
      metricType: 'ratio',
      timestamp: Date.now()
    });
    
    // Agent 2 - high response time
    metricsAPI.recordMetric(agent2Id, agentType, {
      name: 'response_time',
      value: 1500, // Above threshold
      description: 'Response time for operations',
      metricType: 'time',
      timestamp: Date.now()
    });
    
    // Manually trigger the rule check
    await autonomousEvolutionManager['checkEvolutionRules']();
    
    // Verify that evolveAgent was called twice
    expect(evolutionEngine.evolveAgent).toHaveBeenCalledTimes(2);
    
    // Get the evolution history
    const evolutionHistory = autonomousEvolutionManager.getHistory();
    
    // Verify both evolutions are in the history
    expect(evolutionHistory.length).toBe(2);
    
    // Verify agent 1 evolution
    const agent1Evolution = evolutionHistory.find(e => e.agentId === agent1Id);
    expect(agent1Evolution).toBeDefined();
    expect(agent1Evolution?.targetMetrics[0].name).toBe('success_rate');
    
    // Verify agent 2 evolution
    const agent2Evolution = evolutionHistory.find(e => e.agentId === agent2Id);
    expect(agent2Evolution).toBeDefined();
    expect(agent2Evolution?.targetMetrics[0].name).toBe('response_time');
    
    // Verify the high priority rule (response time) was processed first
    expect(evolutionHistory[0].agentId).toBe(agent2Id);
    expect(evolutionHistory[1].agentId).toBe(agent1Id);
  });
  
  test('should forecast metrics and use them for proactive evolution', async () => {
    // Add a rule for proactive evolution
    autonomousEvolutionManager.addRule({
      name: 'Proactive Success Rate Evolution',
      description: 'Evolve agents with declining success rate before it becomes critical',
      condition: {
        metricName: 'success_rate',
        operator: 'less_than',
        threshold: 0.75,
        duration: 0 // No duration for testing
      },
      agentTypes: [], // All agent types
      priority: 'medium',
      cooldownMs: 0, // No cooldown for testing
      enabled: true
    });
    
    // Record historical metrics with a declining trend
    const agentId = 'test-agent-5';
    const agentType = 'EnhancedTestAgent' as AgentType;
    
    // Record metrics with a downward trend
    for (let i = 0; i < 7; i++) {
      metricsAPI.recordMetric(agentId, agentType, {
        name: 'success_rate',
        value: 0.9 - (i * 0.03), // 0.9, 0.87, 0.84, ..., 0.72
        description: 'Success rate of operations',
        metricType: 'ratio',
        timestamp: new Date(Date.now() - (7 - i) * 24 * 60 * 60 * 1000) // One per day
      });
    }
    
    // Forecast future values
    const forecast = metricsAPI.forecastMetric('success_rate', 7, 3, 'day');
    
    // Verify the forecast
    expect(forecast).toBeDefined();
    expect(forecast.forecastData.length).toBe(3);
    
    // The forecast should continue the downward trend
    const lastHistorical = forecast.historicalData[forecast.historicalData.length - 1].value;
    const firstForecast = forecast.forecastData[0].value;
    expect(firstForecast).toBeLessThan(lastHistorical);
    
    // The latest value should be below the threshold
    expect(lastHistorical).toBeLessThan(0.75);
    
    // Manually trigger the rule check
    await autonomousEvolutionManager['checkEvolutionRules']();
    
    // Verify that evolveAgent was called
    expect(evolutionEngine.evolveAgent).toHaveBeenCalled();
    
    // Get the evolution history
    const evolutionHistory = autonomousEvolutionManager.getHistory();
    
    // Verify the evolution is in the history
    expect(evolutionHistory.length).toBeGreaterThan(0);
    const agentEvolution = evolutionHistory.find(e => e.agentId === agentId);
    expect(agentEvolution).toBeDefined();
    expect(agentEvolution?.targetMetrics[0].name).toBe('success_rate');
  });
});

