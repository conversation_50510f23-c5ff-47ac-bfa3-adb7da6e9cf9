import * as Sentry from '@sentry/node';

// Ensure to call this before requiring any other modules!
Sentry.init({
  dsn: process.env.SENTRY_DSN || "https://<EMAIL>/4509520426500096",
  integrations: [
    Sentry.httpIntegration(),
    Sentry.expressIntegration(),
    Sentry.nodeContextIntegration(),
  ],

  // Set tracesSampleRate to 1.0 to capture 100%
  // of transactions for performance monitoring.
  // We recommend adjusting this value in production
  tracesSampleRate: 1.0,

  // Setting this option to true will send default PII data to Sentry.
  // For example, automatic IP address collection on events
  sendDefaultPii: true,

  // Environment and release information
  environment: process.env.NODE_ENV || 'development',
  release: 'alice-agi@1.0.0',

  // Additional options for better error tracking
  beforeSend(event) {
    // Add Alice-specific context
    if (event.extra) {
      event.extra.aliceSystem = 'Alice AGI Backend';
      event.extra.timestamp = new Date().toISOString();
    }
    return event;
  },

  // Configure which errors to capture
  ignoreErrors: [
    // Ignore common non-critical errors
    'Non-Error promise rejection captured',
    'ResizeObserver loop limit exceeded',
  ],
});

export default Sentry;
