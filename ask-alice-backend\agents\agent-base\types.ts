/**
 * Agent Base Types - Unified type definitions for Alice AGI agents
 * This file provides all the core types that agents need to function properly
 */

// Core Agent Types
export interface AgentType {
  id: string;
  name: string;
  type: string;
  status: 'active' | 'inactive' | 'error' | 'initializing';
  capabilities: string[];
  metadata?: Record<string, any>;
}

export interface AgentConfig {
  id: string;
  name: string;
  type: string;
  enabled: boolean;
  settings: Record<string, any>;
}

export interface AgentStatus {
  id: string;
  status: 'active' | 'inactive' | 'error' | 'initializing';
  lastUpdate: Date;
  health: 'healthy' | 'degraded' | 'critical';
  metrics: Record<string, number>;
}

// Message Types
export interface AgentMessage {
  id: string;
  from: string;
  to: string;
  type: string;
  content: any;
  timestamp: Date;
  metadata?: Record<string, any>;
}

export interface MessageResponse {
  success: boolean;
  data?: any;
  error?: string;
  timestamp: Date;
}

// Task Types
export interface AgentTask {
  id: string;
  type: string;
  description: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'pending' | 'running' | 'completed' | 'failed';
  assignedTo?: string;
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export interface TaskResult {
  taskId: string;
  success: boolean;
  result?: any;
  error?: string;
  duration: number;
  timestamp: Date;
}

// Performance Types
export interface PerformanceMetric {
  name: string;
  value: number;
  unit: string;
  timestamp: Date;
  tags?: Record<string, string>;
}

export interface AgentMetrics {
  agentId: string;
  uptime: number;
  tasksCompleted: number;
  tasksFailures: number;
  averageResponseTime: number;
  memoryUsage: number;
  cpuUsage: number;
  customMetrics: Record<string, number>;
}

// Evolution Types
export interface EvolutionMetric {
  generation: number;
  fitness: number;
  improvements: string[];
  timestamp: Date;
}

export interface MutationResult {
  id: string;
  type: string;
  success: boolean;
  fitnessChange: number;
  description: string;
  timestamp: Date;
}

// System Integration Types
export interface SystemHealth {
  status: 'healthy' | 'degraded' | 'critical' | 'unknown';
  uptime: number;
  lastCheck: Date;
  issues: Array<{
    severity: 'low' | 'medium' | 'high' | 'critical';
    message: string;
    timestamp: Date;
  }>;
  metrics: Record<string, number>;
}

export interface SystemStatistics {
  uptime: number;
  operationsCount: number;
  errorsCount: number;
  averageResponseTime: number;
  memoryUsage: number;
  cpuUsage: number;
  customMetrics: Record<string, any>;
}

// Blackboard Types
export interface BlackboardEntry {
  id: string;
  key: string;
  value: any;
  timestamp: Date;
  source: string;
  metadata?: Record<string, any>;
}

export interface BlackboardQuery {
  key?: string;
  source?: string;
  timeRange?: {
    start: Date;
    end: Date;
  };
  limit?: number;
}

// Memory Types
export interface MemoryEntry {
  id: string;
  type: string;
  content: any;
  timestamp: Date;
  importance: number;
  tags: string[];
  metadata?: Record<string, any>;
}

export interface MemoryQuery {
  type?: string;
  tags?: string[];
  timeRange?: {
    start: Date;
    end: Date;
  };
  importance?: {
    min: number;
    max: number;
  };
  limit?: number;
}

// Event Types
export interface AgentEvent {
  id: string;
  type: string;
  source: string;
  data: any;
  timestamp: Date;
  metadata?: Record<string, any>;
}

export interface EventSubscription {
  id: string;
  eventType: string;
  callback: (event: AgentEvent) => void;
  filter?: (event: AgentEvent) => boolean;
}

// Error Types
export interface AgentError {
  id: string;
  type: string;
  message: string;
  stack?: string;
  source: string;
  timestamp: Date;
  severity: 'low' | 'medium' | 'high' | 'critical';
  metadata?: Record<string, any>;
}

// Utility Types
export type AgentId = string;
export type TaskId = string;
export type MessageId = string;
export type EventId = string;

export interface Timestamp {
  created: Date;
  updated: Date;
}

export interface Pagination {
  page: number;
  limit: number;
  total: number;
}

export interface SearchQuery {
  query: string;
  filters?: Record<string, any>;
  sort?: {
    field: string;
    order: 'asc' | 'desc';
  };
  pagination?: Pagination;
}

// Export all types as a namespace for easier importing
export namespace AgentTypes {
  export type Agent = AgentType;
  export type Config = AgentConfig;
  export type Status = AgentStatus;
  export type Message = AgentMessage;
  export type Task = AgentTask;
  export type Metric = PerformanceMetric;
  export type Health = SystemHealth;
  export type Statistics = SystemStatistics;
  export type BlackboardData = BlackboardEntry;
  export type MemoryData = MemoryEntry;
  export type Event = AgentEvent;
  export type Error = AgentError;
}

// Default export for convenience
export default AgentTypes;
