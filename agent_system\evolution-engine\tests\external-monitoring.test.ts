﻿/**
 * External Monitoring Integration Test
 * 
 * Tests the integration with external monitoring tools.
 */

import { MetricsAPI } from '../metrics/api';
import { 
  PrometheusExporter,
  RestApiServer,
  WebhookSender,
  GrafanaExporter
} from '../metrics/external';
import { AgentType } from '../../../../../agents/agent-base/types';
import * as http from 'http';
import * as https from 'https';

describe('External Monitoring Integration', () => {
  let metricsAPI: MetricsAPI;
  
  beforeEach(() => {
    // Create a fresh instance for each test
    metricsAPI = new MetricsAPI();
    
    // Mock http.createServer
    jest.spyOn(http, 'createServer').mockImplementation((requestListener) => {
      const mockServer = {
        listen: jest.fn().mockImplementation((port, callback) => {
          if (callback) callback();
          return mockServer;
        }),
        close: jest.fn().mockImplementation((callback) => {
          if (callback) callback();
          return mockServer;
        })
      } as unknown as http.Server;
      
      return mockServer;
    });
    
    // Mock http.request
    jest.spyOn(http, 'request').mockImplementation(() => {
      const mockRequest = {
        on: jest.fn().mockImplementation((event, callback) => {
          return mockRequest;
        }),
        write: jest.fn(),
        end: jest.fn()
      } as unknown as http.ClientRequest;
      
      return mockRequest;
    });
    
    // Mock https.request
    jest.spyOn(https, 'request').mockImplementation(() => {
      const mockRequest = {
        on: jest.fn().mockImplementation((event, callback) => {
          return mockRequest;
        }),
        write: jest.fn(),
        end: jest.fn()
      } as unknown as http.ClientRequest;
      
      return mockRequest;
    });
  });
  
  afterEach(() => {
    // Clean up any test data
    jest.restoreAllMocks();
  });
  
  test('should start and stop Prometheus exporter', () => {
    // Start the Prometheus exporter
    metricsAPI.startPrometheusExporter(9090, 'alice_');
    
    // Verify that http.createServer was called
    expect(http.createServer).toHaveBeenCalled();
    
    // Get the mock server
    const mockServer = (http.createServer as jest.Mock).mock.results[0].value;
    
    // Verify that listen was called with the correct port
    expect(mockServer.listen).toHaveBeenCalledWith(9090, expect.any(Function));
    
    // Stop the Prometheus exporter
    metricsAPI.stopPrometheusExporter();
    
    // Verify that close was called
    expect(mockServer.close).toHaveBeenCalled();
  });
  
  test('should start and stop REST API server', () => {
    // Start the REST API server
    metricsAPI.startRestApiServer(8080, 'api-key-123');
    
    // Verify that http.createServer was called
    expect(http.createServer).toHaveBeenCalled();
    
    // Get the mock server
    const mockServer = (http.createServer as jest.Mock).mock.results[0].value;
    
    // Verify that listen was called with the correct port
    expect(mockServer.listen).toHaveBeenCalledWith(8080, expect.any(Function));
    
    // Stop the REST API server
    metricsAPI.stopRestApiServer();
    
    // Verify that close was called
    expect(mockServer.close).toHaveBeenCalled();
  });
  
  test('should register and unregister webhooks', () => {
    // Register a webhook
    metricsAPI.registerWebhook(
      'test-webhook',
      'https://example.com/webhook',
      { 'Content-Type': 'application/json' }
    );
    
    // Get all webhooks
    const webhooks = metricsAPI.getWebhooks();
    
    // Verify the webhook was registered
    expect(webhooks['test-webhook']).toBeDefined();
    expect(webhooks['test-webhook'].url).toBe('https://example.com/webhook');
    expect(webhooks['test-webhook'].enabled).toBe(true);
    
    // Unregister the webhook
    const result = metricsAPI.unregisterWebhook('test-webhook');
    
    // Verify the webhook was unregistered
    expect(result).toBe(true);
    
    // Get all webhooks again
    const updatedWebhooks = metricsAPI.getWebhooks();
    
    // Verify the webhook is no longer registered
    expect(updatedWebhooks['test-webhook']).toBeUndefined();
  });
  
  test('should send metrics to webhooks', () => {
    // Create a WebhookSender directly for testing
    const metricsStorage = (metricsAPI as any).metricsAggregator.metricsStorage;
    const webhookSender = new WebhookSender(metricsStorage);
    
    // Register a webhook
    webhookSender.registerWebhook(
      'test-webhook',
      'https://example.com/webhook',
      { 'Content-Type': 'application/json' }
    );
    
    // Send a metric
    const agentId = 'test-agent-1';
    const agentType = 'EnhancedTestAgent' as AgentType;
    
    webhookSender.sendMetric(agentId, agentType, {
      name: 'success_rate',
      value: 0.85,
      description: 'Success rate of operations',
      metricType: 'ratio',
      timestamp: Date.now()
    });
    
    // Verify that https.request was called
    expect(https.request).toHaveBeenCalled();
    
    // Get the mock request
    const mockRequest = (https.request as jest.Mock).mock.results[0].value;
    
    // Verify that write was called with the metric data
    expect(mockRequest.write).toHaveBeenCalled();
    const writtenData = JSON.parse((mockRequest.write as jest.Mock).mock.calls[0][0]);
    
    // Verify the metric data
    expect(writtenData.agentId).toBe(agentId);
    expect(writtenData.agentType).toBe(agentType);
    expect(writtenData.metric.name).toBe('success_rate');
    expect(writtenData.metric.value).toBeCloseTo(0.85);
    
    // Verify that end was called
    expect(mockRequest.end).toHaveBeenCalled();
  });
  
  test('should configure and start Grafana exporter', () => {
    // Configure Grafana exporter
    metricsAPI.configureGrafanaExporter(
      'http://grafana:3000',
      'grafana-api-key',
      'dashboard-uid',
      60000
    );
    
    // Start Grafana exporter
    metricsAPI.startGrafanaExporter();
    
    // Verify that a webhook was registered for Grafana
    const webhooks = metricsAPI.getWebhooks();
    expect(webhooks['grafana']).toBeDefined();
    expect(webhooks['grafana'].url).toContain('grafana:3000');
    expect(webhooks['grafana'].enabled).toBe(true);
    
    // Stop Grafana exporter
    metricsAPI.stopGrafanaExporter();
    
    // Verify that the webhook was unregistered
    const updatedWebhooks = metricsAPI.getWebhooks();
    expect(updatedWebhooks['grafana']).toBeUndefined();
  });
});

