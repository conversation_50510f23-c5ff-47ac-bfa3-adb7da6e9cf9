/**
 * PerformanceMetric.ts
 * 
 * Interface for performance metrics used by the evolution system.
 */

import { AgentType } from '../../../../../agents/agent-base/types';

/**
 * Performance metric interface
 */
export interface PerformanceMetric {
  /**
   * The name of the metric
   */
  name: string;
  
  /**
   * The value of the metric
   */
  value: number;
  
  /**
   * The timestamp when the metric was recorded
   */
  timestamp: Date;
  
  /**
   * The ID of the agent that reported the metric
   */
  agentId: string;
  
  /**
   * The type of the agent that reported the metric
   */
  agentType: AgentType;
  
  /**
   * A description of the metric
   */
  description: string;
  
  /**
   * The unit of the metric (e.g., 'ms', 'ratio', 'count')
   */
  unit: string;
  
  /**
   * Additional context for the metric
   */
  context: Record<string, any>;
}
