// Initialize Sentry BEFORE any other imports
import './instrument';

import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import { ThemeProvider } from './contexts/ThemeContext';
import { ToastProvider } from './contexts/ToastContext';
import { AuthProvider } from './contexts/AuthContext';
import { ChatProvider } from './contexts/ChatContext';
import { BrowserDetachmentProvider } from './contexts/BrowserDetachmentContext';
import './styles/index.css';

// Comment out React Query for now
// import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// Create a client for React Query
// const queryClient = new QueryClient({
//   defaultOptions: {
//     queries: {
//       staleTime: 1000 * 60 * 5, // 5 minutes
//       refetchOnWindowFocus: false,
//       retry: 1,
//     },
//   },
// });

// Render the app with proper context providers
ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <ThemeProvider>
      <ToastProvider>
        <AuthProvider>
          <ChatProvider>
            <BrowserDetachmentProvider>
              <App />
            </BrowserDetachmentProvider>
          </ChatProvider>
        </AuthProvider>
      </ToastProvider>
    </ThemeProvider>
  </React.StrictMode>
);
