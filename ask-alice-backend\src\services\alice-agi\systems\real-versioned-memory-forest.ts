/**
 * Real Versioned Memory Forest Implementation
 * 
 * This implements a real versioned memory forest system with version control,
 * branching, merging, and intelligent version decay.
 */

import { logger } from '../../../utils/logger';

export interface VersionedMemoryNode {
  id: string;
  version: number;
  parentVersion: number | null;
  content: any;
  metadata: {
    importance: number;
    lastAccessed: Date;
    createdAt: Date;
    accessCount: number;
    size: number;
    compressed: boolean;
    author: string;
    changeDescription: string;
  };
  confidence: number;
  branches: string[];
  mergedFrom: string[];
}

export interface VersionedMemoryForest {
  id: string;
  agentId: string;
  versions: Map<string, VersionedMemoryNode>;
  branches: Map<string, string[]>; // branch name -> version ids
  currentBranch: string;
  metadata: {
    totalVersions: number;
    totalSize: number;
    lastDecay: Date;
    compressionRatio: number;
  };
}

export class RealVersionedMemoryForest {
  private forests: Map<string, VersionedMemoryForest> = new Map();
  private decayInterval: NodeJS.Timeout | null = null;
  private compressionInterval: NodeJS.Timeout | null = null;
  private initialized = false;

  private config = {
    maxVersionsPerForest: 1000,
    maxTotalSize: 50 * 1024 * 1024, // 50MB max
    decayInterval: 2 * 60 * 60 * 1000, // 2 hours
    compressionInterval: 4 * 60 * 60 * 1000, // 4 hours
    versionRetentionDays: 30,
    importanceThresholds: {
      critical: 0.9,
      high: 0.7,
      medium: 0.5,
      low: 0.3
    }
  };

  constructor(
    private blackboard: any,
    private memoryForest: any
  ) {}

  async initialize(): Promise<boolean> {
    if (this.initialized) {
      logger.warn('📚 Real Versioned Memory Forest already initialized');
      return true;
    }

    try {
      logger.info('📚 Initializing Real Versioned Memory Forest...');

      // Start decay cycles for version management
      this.startDecayCycles();
      
      // Start compression cycles
      this.startCompressionCycles();

      // Create initial forests for different agents
      await this.createInitialForests();

      this.initialized = true;
      logger.info('✅ Real Versioned Memory Forest initialized successfully!');
      logger.info('🔄 Version decay cycles started');
      logger.info('🗜️ Version compression cycles started');
      logger.info('🌳 Initial versioned forests created');

      return true;
    } catch (error) {
      logger.error('❌ Failed to initialize Real Versioned Memory Forest:', error);
      return false;
    }
  }

  private async createInitialForests(): Promise<void> {
    const agentTypes = ['system', 'user', 'assistant'];
    
    for (const agentType of agentTypes) {
      const forest = this.createVersionedForest(agentType);
      this.forests.set(forest.id, forest);
      logger.info(`🌱 Created versioned forest for ${agentType}: ${forest.id}`);
    }
  }

  private createVersionedForest(agentId: string): VersionedMemoryForest {
    const forestId = `forest-${agentId}-${Date.now()}`;

    const forest: VersionedMemoryForest = {
      id: forestId,
      agentId,
      versions: new Map(),
      branches: new Map([['main', []]]),
      currentBranch: 'main',
      metadata: {
        totalVersions: 0,
        totalSize: 0,
        lastDecay: new Date(),
        compressionRatio: 1.0
      }
    };

    return forest;
  }

  private startDecayCycles(): void {
    this.decayInterval = setInterval(() => {
      this.runVersionDecayCycle();
    }, this.config.decayInterval);

    logger.info('🔄 Version decay cycles started');
  }

  private startCompressionCycles(): void {
    this.compressionInterval = setInterval(() => {
      this.runVersionCompressionCycle();
    }, this.config.compressionInterval);

    logger.info('🗜️ Version compression cycles started');
  }

  private async runVersionDecayCycle(): Promise<void> {
    try {
      logger.debug('🔄 Running versioned memory decay cycle...');

      let totalDecayed = 0;
      let totalMerged = 0;

      for (const forest of this.forests.values()) {
        forest.metadata.lastDecay = new Date();

        // Find old versions to decay
        const oldVersions = Array.from(forest.versions.values()).filter(version => {
          const ageInDays = (Date.now() - version.metadata.createdAt.getTime()) / (1000 * 60 * 60 * 24);
          return ageInDays > this.config.versionRetentionDays;
        });

        for (const version of oldVersions) {
          if (version.metadata.importance < this.config.importanceThresholds.low) {
            // Remove low importance old versions
            await this.removeVersion(forest, version.id);
            totalDecayed++;
          } else if (version.metadata.importance < this.config.importanceThresholds.medium) {
            // Merge medium importance versions with their parents
            await this.mergeVersionWithParent(forest, version);
            totalMerged++;
          }
          // High importance versions are kept
        }

        // Compress branches with too many versions
        await this.compressBranches(forest);
      }

      if (totalDecayed > 0 || totalMerged > 0) {
        logger.info(`🔄 Version decay completed: ${totalDecayed} versions removed, ${totalMerged} versions merged`);
      }
    } catch (error) {
      logger.error('❌ Error in versioned memory decay cycle:', error);
    }
  }

  private async runVersionCompressionCycle(): Promise<void> {
    try {
      logger.debug('🗜️ Running versioned memory compression cycle...');

      const totalSize = this.getTotalSize();
      
      if (totalSize > this.config.maxTotalSize * 0.8) {
        logger.info(`📊 Versioned memory at ${(totalSize / 1024 / 1024).toFixed(1)}MB, starting compression...`);
        
        let totalCompressed = 0;
        let spaceSaved = 0;

        for (const forest of this.forests.values()) {
          for (const version of forest.versions.values()) {
            if (!version.metadata.compressed && version.metadata.size > 1024) {
              const originalSize = version.metadata.size;
              await this.compressVersion(version);
              spaceSaved += originalSize - version.metadata.size;
              totalCompressed++;
            }
          }
          
          // Update compression ratio
          forest.metadata.compressionRatio = spaceSaved > 0 ? spaceSaved / totalSize : 1.0;
        }

        logger.info(`✅ Version compression completed: ${totalCompressed} versions compressed, ${(spaceSaved / 1024 / 1024).toFixed(1)}MB saved`);
      }
    } catch (error) {
      logger.error('❌ Error in versioned memory compression cycle:', error);
    }
  }

  private async compressVersion(version: VersionedMemoryNode): Promise<void> {
    if (version.metadata.compressed) return;

    // Simulate compression by reducing content size
    const originalSize = version.metadata.size;
    version.metadata.size = Math.floor(originalSize * 0.4); // 60% compression
    version.metadata.compressed = true;
    
    // Add compression metadata
    (version.metadata as any).compressionTimestamp = new Date();
    (version.metadata as any).originalSize = originalSize;

    logger.debug(`🗜️ Compressed version ${version.id}: ${originalSize} -> ${version.metadata.size} bytes`);
  }

  private async removeVersion(forest: VersionedMemoryForest, versionId: string): Promise<void> {
    const version = forest.versions.get(versionId);
    if (!version) return;

    // Remove from all branches
    for (const [branchName, versionIds] of forest.branches.entries()) {
      const index = versionIds.indexOf(versionId);
      if (index !== -1) {
        versionIds.splice(index, 1);
      }
    }

    // Remove version
    forest.versions.delete(versionId);
    forest.metadata.totalVersions--;
    forest.metadata.totalSize -= version.metadata.size;

    logger.debug(`🗑️ Removed version ${versionId} from forest ${forest.id}`);
  }

  private async mergeVersionWithParent(forest: VersionedMemoryForest, version: VersionedMemoryNode): Promise<void> {
    if (version.parentVersion === null) return;

    const parentId = `v${version.parentVersion}`;
    const parent = forest.versions.get(parentId);
    if (!parent) return;

    // Merge content and metadata
    parent.content = {
      ...parent.content,
      mergedContent: version.content,
      mergedAt: new Date()
    };
    
    parent.metadata.importance = Math.max(parent.metadata.importance, version.metadata.importance);
    parent.metadata.accessCount += version.metadata.accessCount;
    parent.mergedFrom.push(version.id);

    // Remove the merged version
    await this.removeVersion(forest, version.id);

    logger.debug(`🔀 Merged version ${version.id} into parent ${parentId}`);
  }

  private async compressBranches(forest: VersionedMemoryForest): Promise<void> {
    for (const [branchName, versionIds] of forest.branches.entries()) {
      if (versionIds.length > this.config.maxVersionsPerForest / 2) {
        // Keep only the most recent and most important versions
        const versions = versionIds.map(id => forest.versions.get(id)).filter(Boolean) as VersionedMemoryNode[];
        
        versions.sort((a, b) => {
          // Sort by importance and recency
          const importanceScore = b.metadata.importance - a.metadata.importance;
          const recencyScore = b.metadata.lastAccessed.getTime() - a.metadata.lastAccessed.getTime();
          return importanceScore * 0.7 + recencyScore * 0.3;
        });

        // Keep top 50% of versions
        const keepCount = Math.floor(versions.length / 2);
        const versionsToRemove = versions.slice(keepCount);

        for (const version of versionsToRemove) {
          await this.removeVersion(forest, version.id);
        }

        logger.debug(`🌿 Compressed branch ${branchName}: removed ${versionsToRemove.length} versions`);
      }
    }
  }

  async createVersion(agentId: string, content: any, importance: number = 0.5, changeDescription: string = ''): Promise<string | null> {
    try {
      // Find or create forest for this agent
      let forest = Array.from(this.forests.values()).find(f => f.agentId === agentId);
      
      if (!forest) {
        forest = this.createVersionedForest(agentId);
        this.forests.set(forest.id, forest);
      }

      // Create new version
      const versionNumber = forest.metadata.totalVersions + 1;
      const versionId = `v${versionNumber}`;

      // Find parent version (latest in current branch)
      const currentBranchVersions = forest.branches.get(forest.currentBranch) || [];
      const parentVersion = currentBranchVersions.length > 0 ? 
        parseInt(currentBranchVersions[currentBranchVersions.length - 1].substring(1)) : null;

      const newVersion: VersionedMemoryNode = {
        id: versionId,
        version: versionNumber,
        parentVersion,
        content,
        metadata: {
          importance,
          lastAccessed: new Date(),
          createdAt: new Date(),
          accessCount: 1,
          size: JSON.stringify(content).length,
          compressed: false,
          author: agentId,
          changeDescription
        },
        confidence: importance,
        branches: [forest.currentBranch],
        mergedFrom: []
      };

      // Add to forest
      forest.versions.set(versionId, newVersion);
      forest.metadata.totalVersions++;
      forest.metadata.totalSize += newVersion.metadata.size;

      // Add to current branch
      const branchVersions = forest.branches.get(forest.currentBranch) || [];
      branchVersions.push(versionId);
      forest.branches.set(forest.currentBranch, branchVersions);

      logger.debug(`📝 Created version ${versionId} in forest ${forest.id} (branch: ${forest.currentBranch})`);
      return versionId;
    } catch (error) {
      logger.error('❌ Failed to create version:', error);
      return null;
    }
  }

  async createBranch(agentId: string, branchName: string, fromVersion?: string): Promise<boolean> {
    try {
      const forest = Array.from(this.forests.values()).find(f => f.agentId === agentId);
      if (!forest) return false;

      if (forest.branches.has(branchName)) {
        logger.warn(`Branch ${branchName} already exists in forest ${forest.id}`);
        return false;
      }

      // Create new branch
      const branchVersions: string[] = [];
      
      if (fromVersion && forest.versions.has(fromVersion)) {
        // Branch from specific version
        branchVersions.push(fromVersion);
        const version = forest.versions.get(fromVersion)!;
        version.branches.push(branchName);
      }

      forest.branches.set(branchName, branchVersions);
      logger.info(`🌿 Created branch ${branchName} in forest ${forest.id}`);
      return true;
    } catch (error) {
      logger.error('❌ Failed to create branch:', error);
      return false;
    }
  }

  private getTotalSize(): number {
    let totalSize = 0;
    for (const forest of this.forests.values()) {
      totalSize += forest.metadata.totalSize;
    }
    return totalSize;
  }

  async storeMemory(memoryId: string, content: any, importance: number = 0.5, agentId: string = 'system'): Promise<string | null> {
    try {
      // Use the existing createVersion method to store the memory
      const versionId = await this.createVersion(agentId, content, importance, `Stored memory ${memoryId}`);

      if (versionId) {
        logger.debug(`📚 Stored memory ${memoryId} as version ${versionId}`);
        return versionId;
      } else {
        logger.error(`❌ Failed to store memory ${memoryId}`);
        return null;
      }
    } catch (error) {
      logger.error('❌ Failed to store memory:', error);
      return null;
    }
  }

  async retrieveMemory(versionId: string): Promise<any> {
    try {
      // Search through all forests to find the version
      for (const forest of this.forests.values()) {
        const version = forest.versions.get(versionId);
        if (version) {
          // Update access metadata
          version.metadata.lastAccessed = new Date();
          version.metadata.accessCount++;

          logger.debug(`📚 Retrieved memory version ${versionId} from forest ${forest.id}`);

          // Return the version content with metadata
          return {
            id: version.id,
            version: version.version,
            content: version.content,
            metadata: {
              ...version.metadata,
              forestId: forest.id,
              agentId: forest.agentId,
              parentVersion: version.parentVersion,
              branches: version.branches,
              mergedFrom: version.mergedFrom,
              confidence: version.confidence
            }
          };
        }
      }

      logger.warn(`⚠️ Memory version not found: ${versionId}`);
      return null;
    } catch (error) {
      logger.error('❌ Failed to retrieve memory:', error);
      return null;
    }
  }

  async searchVersions(query: string, agentId?: string, limit: number = 10): Promise<any[]> {
    try {
      const results: any[] = [];
      const queryLower = query.toLowerCase();

      for (const forest of this.forests.values()) {
        // Filter by agent if specified
        if (agentId && forest.agentId !== agentId) continue;

        for (const version of forest.versions.values()) {
          // Simple content matching
          const contentStr = JSON.stringify(version.content).toLowerCase();
          if (contentStr.includes(queryLower)) {
            results.push({
              id: version.id,
              version: version.version,
              content: version.content,
              metadata: {
                ...version.metadata,
                forestId: forest.id,
                agentId: forest.agentId,
                parentVersion: version.parentVersion,
                branches: version.branches,
                mergedFrom: version.mergedFrom,
                confidence: version.confidence,
                relevanceScore: this.calculateRelevanceScore(contentStr, queryLower)
              }
            });
          }
        }
      }

      // Sort by relevance and importance
      results.sort((a, b) => {
        const scoreA = a.metadata.relevanceScore * a.metadata.importance;
        const scoreB = b.metadata.relevanceScore * b.metadata.importance;
        return scoreB - scoreA;
      });

      return results.slice(0, limit);
    } catch (error) {
      logger.error('❌ Failed to search versions:', error);
      return [];
    }
  }

  private calculateRelevanceScore(content: string, query: string): number {
    // Simple relevance scoring based on query matches
    const matches = (content.match(new RegExp(query, 'gi')) || []).length;
    const contentLength = content.length;
    return Math.min(1.0, matches / Math.max(1, contentLength / 100));
  }

  getStatus(): any {
    return {
      initialized: this.initialized,
      totalForests: this.forests.size,
      totalVersions: Array.from(this.forests.values()).reduce((sum, forest) => sum + forest.metadata.totalVersions, 0),
      totalSize: this.getTotalSize(),
      decayActive: !!this.decayInterval,
      compressionActive: !!this.compressionInterval,
      forests: Array.from(this.forests.values()).map(forest => ({
        id: forest.id,
        agentId: forest.agentId,
        versions: forest.metadata.totalVersions,
        branches: forest.branches.size,
        currentBranch: forest.currentBranch,
        size: forest.metadata.totalSize,
        compressionRatio: forest.metadata.compressionRatio
      }))
    };
  }

  async shutdown(): Promise<void> {
    if (this.decayInterval) {
      clearInterval(this.decayInterval);
      this.decayInterval = null;
    }
    
    if (this.compressionInterval) {
      clearInterval(this.compressionInterval);
      this.compressionInterval = null;
    }

    this.initialized = false;
    logger.info('📚 Real Versioned Memory Forest shutdown complete');
  }
}
