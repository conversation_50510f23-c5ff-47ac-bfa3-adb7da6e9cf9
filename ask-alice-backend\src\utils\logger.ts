import * as winston from 'winston';
import * as fs from 'fs';
import * as path from 'path';

// Ensure logs directory exists
const logsDir = path.join(__dirname, '../../logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Clean up large log files (keep under 100KB each - much smaller!)
const cleanupLogFile = (filePath: string, maxSize: number = 100 * 1024) => {
  try {
    if (fs.existsSync(filePath)) {
      const stats = fs.statSync(filePath);
      if (stats.size > maxSize) {
        // Keep only the last 20% of the file to make it very small
        const content = fs.readFileSync(filePath, 'utf8');
        const lines = content.split('\n');
        const keepLines = Math.floor(lines.length * 0.2);
        const newContent = lines.slice(-keepLines).join('\n');
        fs.writeFileSync(filePath, newContent);
        console.log(`📝 Cleaned up log file: ${filePath} (was ${(stats.size / 1024).toFixed(2)}KB, now ${(newContent.length / 1024).toFixed(2)}KB)`);
      }
    }
  } catch (error) {
    console.warn(`⚠️ Failed to cleanup log file ${filePath}:`, error);
  }
};

// Delete existing large log files completely and start fresh
const errorLogPath = path.join(logsDir, 'error.log');
const combinedLogPath = path.join(logsDir, 'combined.log');

try {
  if (fs.existsSync(errorLogPath)) {
    fs.unlinkSync(errorLogPath);
    console.log('🗑️ Deleted large error.log file');
  }
  if (fs.existsSync(combinedLogPath)) {
    fs.unlinkSync(combinedLogPath);
    console.log('🗑️ Deleted large combined.log file');
  }
} catch (error) {
  console.warn('⚠️ Failed to delete large log files:', error);
}

// Define verbose console format
const consoleFormat = winston.format.combine(
  winston.format.timestamp({ format: 'HH:mm:ss.SSS' }),
  winston.format.colorize(),
  winston.format.printf(({ level, message, timestamp, ...meta }) => {
    const metaStr = Object.keys(meta).length ? ` ${JSON.stringify(meta)}` : '';
    return `[${timestamp}] ${level}: ${message}${metaStr}`;
  })
);

// Define file format (more compact)
const fileFormat = winston.format.combine(
  winston.format.timestamp(),
  winston.format.errors({ stack: true }),
  winston.format.json()
);

// Create logger with verbose settings - FORCE CONSOLE OUTPUT
export const logger = winston.createLogger({
  level: 'debug', // Set to debug for maximum verbosity
  defaultMeta: { service: 'ask-alice-backend' },
  transports: [
    // Console transport - very verbose with forced output
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.timestamp({ format: 'HH:mm:ss.SSS' }),
        winston.format.colorize(),
        winston.format.printf(({ level, message, timestamp, ...meta }) => {
          const metaStr = Object.keys(meta).length ? ` ${JSON.stringify(meta)}` : '';
          const output = `[${timestamp}] ${level}: ${message}${metaStr}`;
          // Force immediate console output
          console.log(output);
          return output;
        })
      ),
      level: 'debug',
      handleExceptions: true,
      handleRejections: true
    }),
    // Temporarily disable file transports to focus on console output
    // new winston.transports.File({
    //   filename: path.join(logsDir, 'error.log'),
    //   level: 'error',
    //   format: fileFormat,
    //   maxsize: 50 * 1024, // 50KB max - very small!
    //   maxFiles: 1
    // }),
    // new winston.transports.File({
    //   filename: path.join(logsDir, 'combined.log'),
    //   format: fileFormat,
    //   maxsize: 50 * 1024, // 50KB max - very small!
    //   maxFiles: 1
    // }),
  ],
});

// Add frequent cleanup to keep files very small
setInterval(() => {
  cleanupLogFile(path.join(logsDir, 'error.log'), 30 * 1024); // 30KB max
  cleanupLogFile(path.join(logsDir, 'combined.log'), 30 * 1024); // 30KB max
}, 2 * 60 * 1000); // Every 2 minutes

// Also add immediate console output to ensure we can see what's happening
console.log('🔧 Logger initialized with verbose console output and small file sizes');
console.log(`📁 Log directory: ${logsDir}`);
console.log('📝 File size limits: 50KB max, cleanup every 2 minutes');

