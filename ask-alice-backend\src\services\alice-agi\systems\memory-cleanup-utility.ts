import { logger } from '../../../utils/logger'
import * as fs from 'fs/promises'
import * as path from 'path'

/**
 * Memory Cleanup Utility
 * 
 * Immediately cleans up the bloated memory systems that were consuming 22GB+
 * and thousands of files. This is a one-time cleanup utility.
 */
export class MemoryCleanupUtility {
  private basePath: string

  constructor() {
    this.basePath = process.cwd()
  }

  async performEmergencyCleanup(): Promise<void> {
    logger.info('🚨 Starting EMERGENCY memory cleanup...')
    
    const cleanupStats = {
      directoriesRemoved: 0,
      filesRemoved: 0,
      spaceFreed: 0,
      errors: 0
    }

    // Define all the bloated memory paths
    const bloatedPaths = [
      // Legacy memory systems (NOTE: alice-memory-storage is now our current storage, don't delete it)
      'alice-perpetual-memory',
      'alice-ultimate-memory',
      'memory-exports',
      
      // Experiment spam
      'alice-sandbox/experiments',
      
      // Screenshot accumulation
      'alice-mirror-*.png',
      'alice-action-*.png',
      'alice-screenshot-*.png',
      
      // Test files
      'batch*.txt',
      '*-test*.txt',
      '*-final*.txt',
      '*-fixed*.txt',
      '*-victory*.txt',
      '*-ultimate*.txt',
      '*-clean*.txt',
      
      // Log files
      'combined.log',
      'error.log',
      'build.log',
      'server-logs.txt',
      'errors.txt',
      'initialization-log.txt',
      'temp_errors*.txt'
    ]

    for (const pathPattern of bloatedPaths) {
      try {
        if (pathPattern.includes('*')) {
          // Handle wildcard patterns
          await this.cleanupWildcardPattern(pathPattern, cleanupStats)
        } else {
          // Handle direct paths
          await this.cleanupDirectPath(pathPattern, cleanupStats)
        }
      } catch (error) {
        logger.error(`❌ Error cleaning ${pathPattern}:`, error)
        cleanupStats.errors++
      }
    }

    // Clean up node_modules if it's huge (can be regenerated)
    await this.cleanupNodeModulesIfNeeded(cleanupStats)

    // Clean up dist directory (can be regenerated)
    await this.cleanupDistDirectory(cleanupStats)

    logger.info(`✅ Emergency cleanup completed!`)
    logger.info(`📊 Cleanup Stats:`)
    logger.info(`   - Directories removed: ${cleanupStats.directoriesRemoved}`)
    logger.info(`   - Files removed: ${cleanupStats.filesRemoved}`)
    logger.info(`   - Space freed: ${(cleanupStats.spaceFreed / 1024 / 1024 / 1024).toFixed(2)} GB`)
    logger.info(`   - Errors: ${cleanupStats.errors}`)
  }

  private async cleanupDirectPath(relativePath: string, stats: any): Promise<void> {
    const fullPath = path.join(this.basePath, relativePath)
    
    try {
      const pathStats = await fs.stat(fullPath)
      
      if (pathStats.isDirectory()) {
        const fileCount = await this.countFilesRecursively(fullPath)
        const dirSize = await this.calculateDirectorySize(fullPath)
        
        await fs.rm(fullPath, { recursive: true, force: true })
        
        stats.directoriesRemoved++
        stats.filesRemoved += fileCount
        stats.spaceFreed += dirSize
        
        logger.info(`🗑️ Removed directory: ${relativePath} (${fileCount} files, ${(dirSize / 1024 / 1024).toFixed(1)}MB)`)
      } else if (pathStats.isFile()) {
        const fileSize = pathStats.size
        
        await fs.unlink(fullPath)
        
        stats.filesRemoved++
        stats.spaceFreed += fileSize
        
        logger.info(`🗑️ Removed file: ${relativePath} (${(fileSize / 1024 / 1024).toFixed(1)}MB)`)
      }
    } catch (error: any) {
      if (error.code !== 'ENOENT') {
        logger.debug(`Path not found (OK): ${relativePath}`)
      }
    }
  }

  private async cleanupWildcardPattern(pattern: string, stats: any): Promise<void> {
    const dir = path.dirname(pattern)
    const filename = path.basename(pattern)
    const searchDir = dir === '.' ? this.basePath : path.join(this.basePath, dir)
    
    try {
      const entries = await fs.readdir(searchDir, { withFileTypes: true })
      
      for (const entry of entries) {
        if (this.matchesPattern(entry.name, filename)) {
          const fullPath = path.join(searchDir, entry.name)
          const relativePath = path.relative(this.basePath, fullPath)
          
          try {
            if (entry.isFile()) {
              const fileStats = await fs.stat(fullPath)
              await fs.unlink(fullPath)
              
              stats.filesRemoved++
              stats.spaceFreed += fileStats.size
              
              logger.debug(`🗑️ Removed file: ${relativePath}`)
            } else if (entry.isDirectory()) {
              const fileCount = await this.countFilesRecursively(fullPath)
              const dirSize = await this.calculateDirectorySize(fullPath)
              
              await fs.rm(fullPath, { recursive: true, force: true })
              
              stats.directoriesRemoved++
              stats.filesRemoved += fileCount
              stats.spaceFreed += dirSize
              
              logger.info(`🗑️ Removed directory: ${relativePath} (${fileCount} files)`)
            }
          } catch (error) {
            logger.warn(`⚠️ Failed to remove ${relativePath}:`, error)
            stats.errors++
          }
        }
      }
    } catch (error) {
      logger.debug(`Directory not found: ${searchDir}`)
    }
  }

  private matchesPattern(filename: string, pattern: string): boolean {
    // Simple wildcard matching
    const regex = new RegExp('^' + pattern.replace(/\*/g, '.*') + '$')
    return regex.test(filename)
  }

  private async countFilesRecursively(dirPath: string): Promise<number> {
    try {
      const entries = await fs.readdir(dirPath, { withFileTypes: true })
      let count = 0
      
      for (const entry of entries) {
        if (entry.isFile()) {
          count++
        } else if (entry.isDirectory()) {
          count += await this.countFilesRecursively(path.join(dirPath, entry.name))
        }
      }
      
      return count
    } catch (error) {
      return 0
    }
  }

  private async calculateDirectorySize(dirPath: string): Promise<number> {
    try {
      const entries = await fs.readdir(dirPath, { withFileTypes: true })
      let size = 0
      
      for (const entry of entries) {
        const fullPath = path.join(dirPath, entry.name)
        if (entry.isFile()) {
          const stats = await fs.stat(fullPath)
          size += stats.size
        } else if (entry.isDirectory()) {
          size += await this.calculateDirectorySize(fullPath)
        }
      }
      
      return size
    } catch (error) {
      return 0
    }
  }

  private async cleanupNodeModulesIfNeeded(stats: any): Promise<void> {
    const nodeModulesPath = path.join(this.basePath, 'node_modules')
    
    try {
      const nodeModulesSize = await this.calculateDirectorySize(nodeModulesPath)
      
      // If node_modules is over 1GB, remove it (can be regenerated with npm install)
      if (nodeModulesSize > 1024 * 1024 * 1024) {
        const fileCount = await this.countFilesRecursively(nodeModulesPath)
        
        await fs.rm(nodeModulesPath, { recursive: true, force: true })
        
        stats.directoriesRemoved++
        stats.filesRemoved += fileCount
        stats.spaceFreed += nodeModulesSize
        
        logger.info(`🗑️ Removed large node_modules: ${(nodeModulesSize / 1024 / 1024 / 1024).toFixed(2)}GB (run 'npm install' to regenerate)`)
      }
    } catch (error) {
      logger.debug('node_modules not found or inaccessible')
    }
  }

  private async cleanupDistDirectory(stats: any): Promise<void> {
    const distPath = path.join(this.basePath, 'dist')
    
    try {
      const distSize = await this.calculateDirectorySize(distPath)
      const fileCount = await this.countFilesRecursively(distPath)
      
      await fs.rm(distPath, { recursive: true, force: true })
      
      stats.directoriesRemoved++
      stats.filesRemoved += fileCount
      stats.spaceFreed += distSize
      
      logger.info(`🗑️ Removed dist directory: ${(distSize / 1024 / 1024).toFixed(1)}MB (will be regenerated on build)`)
    } catch (error) {
      logger.debug('dist directory not found')
    }
  }

  async createCleanupReport(): Promise<string> {
    const report = []
    report.push('# Alice Memory Cleanup Report')
    report.push('')
    report.push('## Issues Found:')
    
    const bloatedPaths = [
      'alice-perpetual-memory',
      'alice-ultimate-memory',
      'memory-exports',
      'alice-sandbox/experiments'
    ]

    for (const pathName of bloatedPaths) {
      const fullPath = path.join(this.basePath, pathName)
      try {
        const stats = await fs.stat(fullPath)
        if (stats.isDirectory()) {
          const fileCount = await this.countFilesRecursively(fullPath)
          const size = await this.calculateDirectorySize(fullPath)
          report.push(`- **${pathName}**: ${fileCount} files, ${(size / 1024 / 1024).toFixed(1)}MB`)
        }
      } catch (error) {
        report.push(`- **${pathName}**: Not found`)
      }
    }

    report.push('')
    report.push('## Recommended Actions:')
    report.push('1. Run emergency cleanup to remove bloated memory systems')
    report.push('2. Implement Intelligent Perpetual Memory System')
    report.push('3. Set up proper memory limits and cleanup schedules')
    report.push('4. Monitor memory usage going forward')

    return report.join('\n')
  }
}

// Standalone cleanup function for immediate use
export async function runEmergencyMemoryCleanup(): Promise<void> {
  const cleanup = new MemoryCleanupUtility()
  await cleanup.performEmergencyCleanup()
}
