import React, { useState } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkMath from 'remark-math';
import rehypeKatex from 'rehype-katex';
import rehypeHighlight from 'rehype-highlight';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { oneDark, oneLight } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { useTheme } from '../../hooks/useTheme';
import {
  ClipboardIcon,
  CheckIcon,
  InformationCircleIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon,
  LightBulbIcon,
  BookOpenIcon,
  CodeBracketIcon,
  ChevronDownIcon,
  ChevronRightIcon,
} from '@heroicons/react/24/outline';
import 'katex/dist/katex.min.css';

interface EnhancedMarkdownRendererProps {
  content: string;
  className?: string;
}

interface CalloutProps {
  type: 'note' | 'tip' | 'info' | 'warning' | 'danger' | 'success' | 'example';
  title?: string;
  children: React.ReactNode;
  collapsible?: boolean;
}

const Callout: React.FC<CalloutProps> = ({ type, title, children, collapsible = false }) => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  
  const calloutConfig = {
    note: {
      icon: BookOpenIcon,
      bgColor: 'bg-blue-50 dark:bg-blue-900/20',
      borderColor: 'border-blue-200 dark:border-blue-700',
      iconColor: 'text-blue-600 dark:text-blue-400',
      titleColor: 'text-blue-800 dark:text-blue-300',
      defaultTitle: 'Note'
    },
    tip: {
      icon: LightBulbIcon,
      bgColor: 'bg-green-50 dark:bg-green-900/20',
      borderColor: 'border-green-200 dark:border-green-700',
      iconColor: 'text-green-600 dark:text-green-400',
      titleColor: 'text-green-800 dark:text-green-300',
      defaultTitle: 'Tip'
    },
    info: {
      icon: InformationCircleIcon,
      bgColor: 'bg-cyan-50 dark:bg-cyan-900/20',
      borderColor: 'border-cyan-200 dark:border-cyan-700',
      iconColor: 'text-cyan-600 dark:text-cyan-400',
      titleColor: 'text-cyan-800 dark:text-cyan-300',
      defaultTitle: 'Info'
    },
    warning: {
      icon: ExclamationTriangleIcon,
      bgColor: 'bg-yellow-50 dark:bg-yellow-900/20',
      borderColor: 'border-yellow-200 dark:border-yellow-700',
      iconColor: 'text-yellow-600 dark:text-yellow-400',
      titleColor: 'text-yellow-800 dark:text-yellow-300',
      defaultTitle: 'Warning'
    },
    danger: {
      icon: XCircleIcon,
      bgColor: 'bg-red-50 dark:bg-red-900/20',
      borderColor: 'border-red-200 dark:border-red-700',
      iconColor: 'text-red-600 dark:text-red-400',
      titleColor: 'text-red-800 dark:text-red-300',
      defaultTitle: 'Danger'
    },
    success: {
      icon: CheckCircleIcon,
      bgColor: 'bg-emerald-50 dark:bg-emerald-900/20',
      borderColor: 'border-emerald-200 dark:border-emerald-700',
      iconColor: 'text-emerald-600 dark:text-emerald-400',
      titleColor: 'text-emerald-800 dark:text-emerald-300',
      defaultTitle: 'Success'
    },
    example: {
      icon: CodeBracketIcon,
      bgColor: 'bg-purple-50 dark:bg-purple-900/20',
      borderColor: 'border-purple-200 dark:border-purple-700',
      iconColor: 'text-purple-600 dark:text-purple-400',
      titleColor: 'text-purple-800 dark:text-purple-300',
      defaultTitle: 'Example'
    }
  };

  const config = calloutConfig[type];
  const Icon = config.icon;
  const displayTitle = title || config.defaultTitle;

  return (
    <div className={`my-4 rounded-lg border-l-4 ${config.bgColor} ${config.borderColor} overflow-hidden`}>
      <div 
        className={`flex items-center gap-3 p-4 ${collapsible ? 'cursor-pointer' : ''}`}
        onClick={collapsible ? () => setIsCollapsed(!isCollapsed) : undefined}
      >
        <Icon className={`w-5 h-5 flex-shrink-0 ${config.iconColor}`} />
        <h4 className={`font-semibold ${config.titleColor} flex-1`}>
          {displayTitle}
        </h4>
        {collapsible && (
          <div className={config.iconColor}>
            {isCollapsed ? (
              <ChevronRightIcon className="w-4 h-4" />
            ) : (
              <ChevronDownIcon className="w-4 h-4" />
            )}
          </div>
        )}
      </div>
      {(!collapsible || !isCollapsed) && (
        <div className="px-4 pb-4">
          <div className="prose prose-sm dark:prose-invert max-w-none">
            {children}
          </div>
        </div>
      )}
    </div>
  );
};

const CodeBlock: React.FC<{ language?: string; children: string }> = ({ language, children }) => {
  const { resolvedTheme } = useTheme();
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    await navigator.clipboard.writeText(children);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <div className="relative group my-4">
      <div className="absolute right-2 top-2 opacity-0 group-hover:opacity-100 transition-opacity z-10">
        <button
          type="button"
          onClick={handleCopy}
          className="p-2 rounded-md bg-light-background-primary/90 dark:bg-dark-background-primary/90 backdrop-blur-sm hover:bg-light-background-secondary dark:hover:bg-dark-background-secondary transition-colors"
          aria-label="Copy code"
        >
          {copied ? (
            <CheckIcon className="w-4 h-4 text-green-500" />
          ) : (
            <ClipboardIcon className="w-4 h-4" />
          )}
        </button>
      </div>
      <SyntaxHighlighter
        style={resolvedTheme === 'dark' ? oneDark : oneLight}
        language={language || 'text'}
        PreTag="div"
        className="rounded-lg !mt-0 !mb-0"
        showLineNumbers={true}
        wrapLines={true}
        lineProps={{
          style: { display: 'block', width: 'fit-content', minWidth: '100%' }
        }}
      >
        {children}
      </SyntaxHighlighter>
    </div>
  );
};

export const EnhancedMarkdownRenderer: React.FC<EnhancedMarkdownRendererProps> = ({
  content,
  className = ''
}) => {
  const { resolvedTheme } = useTheme();

  // Parse callouts from markdown content
  const parseCallouts = (text: string) => {
    // Match callout syntax: > [!type] Title (optional)
    const calloutRegex = /^>\s*\[!(note|tip|info|warning|danger|success|example)\]\s*(.*?)$/gm;

    return text.replace(calloutRegex, (match, type, title) => {
      return `<Callout type="${type}" title="${title.trim() || ''}">\n`;
    });
  };

  const processedContent = parseCallouts(content);

  return (
    <div className={`enhanced-markdown prose prose-lg dark:prose-invert max-w-none ${className}`}>
      <ReactMarkdown
        remarkPlugins={[remarkGfm, remarkMath]}
        rehypePlugins={[rehypeKatex]}
        components={{
          // Beautiful modern headings with emojis
          h1: ({ children }) => (
            <h1 className="text-3xl font-bold mt-8 mb-6 first:mt-0 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent leading-tight">
              {children}
            </h1>
          ),
          h2: ({ children }) => (
            <h2 className="text-2xl font-semibold mt-6 mb-4 text-slate-800 dark:text-slate-100 border-b border-slate-200 dark:border-slate-700 pb-2">
              {children}
            </h2>
          ),
          h3: ({ children }) => (
            <h3 className="text-xl font-semibold mt-5 mb-3 text-slate-700 dark:text-slate-200">
              {children}
            </h3>
          ),
          h4: ({ children }) => (
            <h4 className="text-lg font-medium mt-4 mb-2 text-slate-700 dark:text-slate-200">
              {children}
            </h4>
          ),
          h5: ({ children }) => (
            <h5 className="text-base font-medium mt-3 mb-2 text-slate-600 dark:text-slate-300">
              {children}
            </h5>
          ),
          h6: ({ children }) => (
            <h6 className="text-sm font-medium mt-3 mb-2 text-slate-500 dark:text-slate-400 uppercase tracking-wide">
              {children}
            </h6>
          ),

          // Beautiful paragraphs with proper spacing
          p: ({ children }) => (
            <p className="text-slate-700 dark:text-slate-300 leading-relaxed mb-4 text-base">
              {children}
            </p>
          ),
          
          // Beautiful modern lists
          ul: ({ children }) => (
            <ul className="space-y-2 mb-6 pl-6">
              {children}
            </ul>
          ),
          ol: ({ children }) => (
            <ol className="space-y-2 mb-6 pl-6">
              {children}
            </ol>
          ),
          li: ({ children }) => (
            <li className="text-slate-700 dark:text-slate-300 leading-relaxed marker:text-blue-500">
              {children}
            </li>
          ),

          // Beautiful modern blockquotes
          blockquote: ({ children }) => (
            <blockquote className="border-l-4 border-blue-500 bg-blue-50 dark:bg-blue-900/20 pl-6 pr-4 py-4 my-6 rounded-r-lg">
              <div className="text-slate-700 dark:text-slate-300 italic text-lg leading-relaxed">
                {children}
              </div>
            </blockquote>
          ),

          // Beautiful modern code blocks
          code: ({ node, inline, className, children, ...props }) => {
            const match = /language-(\w+)/.exec(className || '');
            const codeContent = String(children).replace(/\n$/, '');

            if (!inline && match) {
              return (
                <div className="my-6">
                  <CodeBlock language={match[1]}>{codeContent}</CodeBlock>
                </div>
              );
            }

            return (
              <code
                className="px-2 py-1 rounded-md bg-slate-100 dark:bg-slate-800 text-purple-600 dark:text-purple-400 font-mono text-sm border border-slate-200 dark:border-slate-700"
                {...props}
              >
                {children}
              </code>
            );
          },

          // Beautiful modern tables
          table: ({ children }) => (
            <div className="my-6 overflow-x-auto">
              <table className="min-w-full border-collapse border border-slate-300 dark:border-slate-600 rounded-lg overflow-hidden shadow-lg bg-white dark:bg-slate-900">
                {children}
              </table>
            </div>
          ),
          thead: ({ children }) => (
            <thead className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/30 dark:to-purple-900/30">
              {children}
            </thead>
          ),
          tbody: ({ children }) => (
            <tbody>
              {children}
            </tbody>
          ),
          tr: ({ children }) => (
            <tr className="border-b border-slate-200 dark:border-slate-700 hover:bg-slate-50 dark:hover:bg-slate-800/50 transition-colors">
              {children}
            </tr>
          ),
          th: ({ children }) => (
            <th className="px-6 py-4 text-left font-bold text-slate-800 dark:text-slate-200 border-r border-slate-200 dark:border-slate-700 last:border-r-0">
              {children}
            </th>
          ),
          td: ({ children }) => (
            <td className="px-6 py-4 text-slate-700 dark:text-slate-300 border-r border-slate-200 dark:border-slate-700 last:border-r-0">
              {children}
            </td>
          ),

          // Beautiful callouts
          Callout: ({ type, title, children }: { type: string; title: string; children: React.ReactNode }) => (
            <Callout type={type as any} title={title}>
              {children}
            </Callout>
          ),

          // Beautiful modern links
          a: ({ href, children }) => (
            <a
              href={href}
              className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 underline decoration-blue-300 hover:decoration-blue-500 transition-all duration-200 font-medium"
              target="_blank"
              rel="noopener noreferrer"
            >
              {children}
            </a>
          ),

          // Enhanced emphasis
          strong: ({ children }) => (
            <strong className="font-bold text-slate-800 dark:text-slate-200">
              {children}
            </strong>
          ),
          em: ({ children }) => (
            <em className="italic text-slate-700 dark:text-slate-300">
              {children}
            </em>
          ),

          // Beautiful horizontal rules
          hr: () => (
            <hr className="my-8 border-0 h-px bg-gradient-to-r from-transparent via-slate-300 dark:via-slate-600 to-transparent" />
          ),
        }}
      >
        {processedContent}
      </ReactMarkdown>
    </div>
  );
};
