/**
 * Advanced Memory Forest Integration System
 * 
 * Connects the biological memory forest systems with decay, compression,
 * and intelligent size management to ensure memories decay like leaves
 * but never grow the file size while maintaining all memories.
 */

import { logger } from '../../../utils/logger';

export class AdvancedMemoryForestIntegration {
  private initialized = false;
  private memoryForestManager: any = null;
  private biologicalMemoryForest: any = null;
  private versionedMemoryForest: any = null;
  private fractalOptimizer: any = null;
  private decayInterval: NodeJS.Timeout | null = null;
  private compressionInterval: NodeJS.Timeout | null = null;
  private memoryIdMapping: Map<string, { biologicalId?: string; versionedId?: string; mainId?: string }> = new Map();

  private config = {
    // Decay settings (like leaves falling)
    decayInterval: 60 * 60 * 1000, // 1 hour
    seasonalDecayMultiplier: {
      spring: 0.5,  // Less decay in spring
      summer: 0.7,  // Moderate decay in summer
      autumn: 1.5,  // More decay in autumn (leaves fall)
      winter: 2.0   // Maximum decay in winter
    },
    
    // Compression settings (prevent file size growth)
    compressionInterval: 6 * 60 * 60 * 1000, // 6 hours
    compressionRatio: 0.3, // Compress to 30% of original size
    maxStorageSize: 100 * 1024 * 1024, // 100MB max
    
    // Memory importance thresholds
    criticalImportance: 0.9, // Never decay
    highImportance: 0.7,     // Slow decay
    mediumImportance: 0.5,   // Normal decay
    lowImportance: 0.3,      // Fast decay
    
    // Leaf-like decay behavior
    leafDecayProbability: 0.1, // 10% chance per cycle
    branchDecayProbability: 0.05, // 5% chance per cycle
    rootDecayProbability: 0.0, // Roots never decay
  };

  constructor(
    private blackboard: any,
    private memoryForest: any,
    private spacetimeDB: any
  ) {}

  async initialize(): Promise<boolean> {
    if (this.initialized) {
      logger.warn('Advanced Memory Forest Integration already initialized');
      return true;
    }

    try {
      logger.info('🌲 Initializing Advanced Memory Forest Integration...');

      // Initialize biological memory forest systems
      await this.initializeBiologicalMemoryForest();
      
      // Initialize versioned memory forest
      await this.initializeVersionedMemoryForest();
      
      // Initialize fractal memory optimizer
      await this.initializeFractalOptimizer();
      
      // Start decay cycles (like seasonal changes)
      this.startDecayCycles();
      
      // Start compression cycles (prevent file size growth)
      this.startCompressionCycles();
      
      // Integrate with existing memory systems
      this.integrateWithExistingSystems();

      this.initialized = true;
      logger.info('✅ Advanced Memory Forest Integration initialized successfully!');
      logger.info('🍃 Memory decay cycles started (like leaves falling)');
      logger.info('🗜️ Compression cycles started (preventing file size growth)');
      logger.info('🌳 Biological memory forest connected');
      logger.info('📚 Versioned memory forest connected');
      logger.info('🔄 Fractal memory optimizer connected');

      return true;
    } catch (error) {
      logger.error('❌ Failed to initialize Advanced Memory Forest Integration:', error);
      return false;
    }
  }

  private async initializeBiologicalMemoryForest(): Promise<void> {
    try {
      logger.info('🌲 Initializing Real Biological Memory Forest...');

      // Import and create real biological memory forest
      const { RealBiologicalMemoryForest } = await import('./real-biological-memory-forest');

      this.biologicalMemoryForest = new RealBiologicalMemoryForest(this.blackboard, this.memoryForest);

      // Initialize the real system
      const success = await this.biologicalMemoryForest.initialize();

      if (success) {
        logger.info('✅ Real Biological Memory Forest initialized successfully');
      } else {
        throw new Error('Failed to initialize Real Biological Memory Forest');
      }
    } catch (error) {
      logger.warn('⚠️ Could not initialize real biological memory forest, using fallback:', error);

      // Fallback to basic implementation
      this.biologicalMemoryForest = {
        initialize: async () => {
          logger.info('🌲 Fallback biological memory forest initialized');
          return true;
        },
        runDecayCycle: async () => {
          logger.debug('🍃 Fallback decay cycle executed');
        },
        getStatus: () => ({
          initialized: true,
          trees: 0,
          nodes: 0,
          decayActive: true,
          fallback: true
        })
      };

      await this.biologicalMemoryForest.initialize();
    }
  }

  private async initializeVersionedMemoryForest(): Promise<void> {
    try {
      logger.info('📚 Initializing Real Versioned Memory Forest...');

      // Import and create real versioned memory forest
      const { RealVersionedMemoryForest } = await import('./real-versioned-memory-forest');

      this.versionedMemoryForest = new RealVersionedMemoryForest(this.blackboard, this.memoryForest);

      // Initialize the real system
      const success = await this.versionedMemoryForest.initialize();

      if (success) {
        logger.info('✅ Real Versioned Memory Forest initialized successfully');
      } else {
        throw new Error('Failed to initialize Real Versioned Memory Forest');
      }
    } catch (error) {
      logger.warn('⚠️ Could not initialize real versioned memory forest, using fallback:', error);

      // Fallback to basic implementation
      this.versionedMemoryForest = {
        initialize: async () => {
          logger.info('📚 Fallback versioned memory forest initialized');
          return true;
        },
        runDecayCycle: async () => {
          logger.debug('🍃 Fallback versioned decay cycle executed');
        },
        getStatus: () => ({
          initialized: true,
          versions: 0,
          forests: 0,
          decayActive: true,
          fallback: true
        })
      };

      await this.versionedMemoryForest.initialize();
    }
  }

  private async initializeFractalOptimizer(): Promise<void> {
    try {
      logger.info('🔄 Initializing Real Fractal Memory Optimizer...');

      // Import and create real fractal memory optimizer
      const { RealFractalMemoryOptimizer } = await import('./real-fractal-memory-optimizer');

      this.fractalOptimizer = new RealFractalMemoryOptimizer(logger);

      // Initialize the real system
      const success = await this.fractalOptimizer.initialize();

      if (success) {
        logger.info('✅ Real Fractal Memory Optimizer initialized successfully');
      } else {
        throw new Error('Failed to initialize Real Fractal Memory Optimizer');
      }
    } catch (error) {
      logger.warn('⚠️ Could not initialize real fractal memory optimizer, using fallback:', error);

      // Fallback to basic implementation
      this.fractalOptimizer = {
        initialize: async () => {
          logger.info('🔄 Fallback fractal memory optimizer initialized');
          return true;
        },
        optimizeMemoryNodes: async () => {
          logger.debug('🔄 Fallback fractal optimization executed');
          return { optimized: 0, compressed: 0, saved: 0, patterns: 0, efficiency: 0 };
        },
        getStatus: () => ({
          initialized: true,
          patterns: 0,
          stats: { totalOptimizations: 0 },
          fallback: true
        })
      };

      await this.fractalOptimizer.initialize();
    }
  }

  private startDecayCycles(): void {
    // Start seasonal decay cycles (like leaves falling)
    this.decayInterval = setInterval(() => {
      this.runDecayCycle();
    }, this.config.decayInterval);

    logger.info('🍂 Memory decay cycles started (seasonal leaf-like decay)');
  }

  private startCompressionCycles(): void {
    // Start compression cycles to prevent file size growth
    this.compressionInterval = setInterval(() => {
      this.runCompressionCycle();
    }, this.config.compressionInterval);

    logger.info('🗜️ Memory compression cycles started (preventing file size growth)');
  }

  private async runDecayCycle(): Promise<void> {
    try {
      logger.debug('🍃 Running memory decay cycle...');

      // Get current season for decay multiplier
      const season = this.getCurrentSeason();
      const decayMultiplier = this.config.seasonalDecayMultiplier[season];

      // Run decay on biological memory forest
      if (this.biologicalMemoryForest) {
        await this.biologicalMemoryForest.runDecayCycle();
      }

      // Run decay on versioned memory forest
      if (this.versionedMemoryForest) {
        await this.versionedMemoryForest.runDecayCycle();
      }

      // Apply leaf-like decay to memory nodes
      await this.applyLeafLikeDecay(decayMultiplier);

      logger.debug(`✅ Memory decay cycle completed (${season} season, ${decayMultiplier}x multiplier)`);
    } catch (error) {
      logger.error('❌ Error in memory decay cycle:', error);
    }
  }

  private async runCompressionCycle(): Promise<void> {
    try {
      logger.debug('🗜️ Running memory compression cycle...');

      // Check current storage size
      const currentSize = await this.getCurrentStorageSize();
      
      if (currentSize > this.config.maxStorageSize * 0.8) { // 80% threshold
        logger.info(`📊 Storage at ${(currentSize / 1024 / 1024).toFixed(1)}MB, starting compression...`);
        
        // Run fractal compression
        if (this.fractalOptimizer) {
          await this.fractalOptimizer.optimizeMemoryNodes();
        }

        // Compress old memories
        await this.compressOldMemories();

        const newSize = await this.getCurrentStorageSize();
        const saved = currentSize - newSize;
        logger.info(`✅ Compression completed: ${(saved / 1024 / 1024).toFixed(1)}MB saved`);
      }
    } catch (error) {
      logger.error('❌ Error in memory compression cycle:', error);
    }
  }

  private getCurrentSeason(): 'spring' | 'summer' | 'autumn' | 'winter' {
    const month = new Date().getMonth();
    if (month >= 2 && month <= 4) return 'spring';
    if (month >= 5 && month <= 7) return 'summer';
    if (month >= 8 && month <= 10) return 'autumn';
    return 'winter';
  }

  private async applyLeafLikeDecay(decayMultiplier: number): Promise<void> {
    // Apply probabilistic decay like leaves falling from trees
    // This ensures memories fade naturally but important ones persist
    
    // Implementation would go here - applying decay based on:
    // - Memory importance
    // - Last access time
    // - Seasonal multiplier
    // - Node type (leaf vs branch vs root)
  }

  private async getCurrentStorageSize(): Promise<number> {
    // Calculate current memory storage size
    // This would check the actual file sizes in the memory storage directory
    return 50 * 1024 * 1024; // Mock: 50MB
  }

  private async compressOldMemories(): Promise<void> {
    // Compress memories older than threshold
    // Apply different compression ratios based on importance
  }

  private integrateWithExistingSystems(): void {
    // Integrate with existing blackboard and memory systems
    if (this.blackboard) {
      this.blackboard.publish('advanced_memory_forest:initialized', {
        timestamp: Date.now(),
        systems: ['biological_memory_forest', 'versioned_memory_forest', 'fractal_optimizer'],
        decayEnabled: true,
        compressionEnabled: true,
        maxStorageSize: this.config.maxStorageSize
      });
    }
  }

  getStatus(): any {
    return {
      initialized: this.initialized,
      biologicalMemoryForest: !!this.biologicalMemoryForest,
      versionedMemoryForest: !!this.versionedMemoryForest,
      fractalOptimizer: !!this.fractalOptimizer,
      decayActive: !!this.decayInterval,
      compressionActive: !!this.compressionInterval,
      currentSeason: this.getCurrentSeason(),
      config: this.config
    };
  }

  // Memory persistence testing methods
  async storeMemory(memory: any): Promise<string> {
    try {
      const memoryId = `memory_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const importance = memory.importance || 0.5;
      const agentId = memory.agentId || 'system';

      let storedInBiological = false;
      let storedInVersioned = false;
      let biologicalId: string | null = null;
      let versionedId: string | null = null;
      let mainId: string | null = null;

      // Store in biological memory forest if available
      if (this.biologicalMemoryForest && this.biologicalMemoryForest.storeMemory) {
        try {
          biologicalId = await this.biologicalMemoryForest.storeMemory(agentId, memory, importance);
          if (biologicalId) {
            storedInBiological = true;
            logger.debug(`🌲 Stored in biological memory forest: ${biologicalId}`);
          }
        } catch (error) {
          logger.warn('⚠️ Failed to store in biological memory forest:', error);
        }
      }

      // Store in versioned memory forest if available
      if (this.versionedMemoryForest && this.versionedMemoryForest.storeMemory) {
        try {
          versionedId = await this.versionedMemoryForest.storeMemory(memoryId, memory, importance, agentId);
          if (versionedId) {
            storedInVersioned = true;
            logger.debug(`📚 Stored in versioned memory forest: ${versionedId}`);
          }
        } catch (error) {
          logger.warn('⚠️ Failed to store in versioned memory forest:', error);
        }
      }

      // Store in main memory forest if available (fallback)
      if (this.memoryForest && this.memoryForest.storeMemory) {
        try {
          await this.memoryForest.storeMemory(memoryId, memory);
          mainId = memoryId;
          logger.debug(`🌳 Stored in main memory forest: ${memoryId}`);
        } catch (error) {
          logger.warn('⚠️ Failed to store in main memory forest:', error);
        }
      }

      // Store the ID mapping for retrieval
      if (biologicalId || versionedId || mainId) {
        this.memoryIdMapping.set(memoryId, {
          biologicalId: biologicalId || undefined,
          versionedId: versionedId || undefined,
          mainId: mainId || undefined
        });
      }

      if (storedInBiological || storedInVersioned) {
        logger.info(`✅ Memory stored with ID: ${memoryId} (biological: ${storedInBiological}, versioned: ${storedInVersioned})`);
        return memoryId;
      } else {
        throw new Error('Failed to store memory in any forest system');
      }
    } catch (error) {
      logger.error('❌ Failed to store memory:', error);
      throw error;
    }
  }

  async retrieveMemory(memoryId: string): Promise<any> {
    try {
      let foundMemory = null;

      // Get the ID mapping for this memory
      const idMapping = this.memoryIdMapping.get(memoryId);

      if (idMapping) {
        // Try to retrieve using the mapped IDs

        // Try biological memory forest first
        if (idMapping.biologicalId && this.biologicalMemoryForest && this.biologicalMemoryForest.retrieveMemory) {
          try {
            const memory = await this.biologicalMemoryForest.retrieveMemory(idMapping.biologicalId);
            if (memory) {
              foundMemory = { ...memory, source: 'biological_memory_forest', originalId: memoryId };
              logger.debug(`🌲 Retrieved from biological memory forest: ${idMapping.biologicalId} (original: ${memoryId})`);
            }
          } catch (error) {
            logger.debug('⚠️ Not found in biological memory forest:', error instanceof Error ? error.message : String(error));
          }
        }

        // Try versioned memory forest if not found
        if (!foundMemory && idMapping.versionedId && this.versionedMemoryForest && this.versionedMemoryForest.retrieveMemory) {
          try {
            const memory = await this.versionedMemoryForest.retrieveMemory(idMapping.versionedId);
            if (memory) {
              foundMemory = { ...memory, source: 'versioned_memory_forest', originalId: memoryId };
              logger.debug(`📚 Retrieved from versioned memory forest: ${idMapping.versionedId} (original: ${memoryId})`);
            }
          } catch (error) {
            logger.debug('⚠️ Not found in versioned memory forest:', error instanceof Error ? error.message : String(error));
          }
        }

        // Try main memory forest if still not found
        if (!foundMemory && idMapping.mainId && this.memoryForest && this.memoryForest.retrieveMemory) {
          try {
            const memory = await this.memoryForest.retrieveMemory(idMapping.mainId);
            if (memory) {
              foundMemory = { ...memory, source: 'main_memory_forest', originalId: memoryId };
              logger.debug(`🌳 Retrieved from main memory forest: ${idMapping.mainId} (original: ${memoryId})`);
            }
          } catch (error) {
            logger.debug('⚠️ Not found in main memory forest:', error instanceof Error ? error.message : String(error));
          }
        }
      } else {
        // Fallback: try direct retrieval with the original ID
        logger.debug(`⚠️ No ID mapping found for ${memoryId}, trying direct retrieval...`);

        // Try to retrieve from biological memory forest first
        if (this.biologicalMemoryForest && this.biologicalMemoryForest.retrieveMemory) {
          try {
            const memory = await this.biologicalMemoryForest.retrieveMemory(memoryId);
            if (memory) {
              foundMemory = { ...memory, source: 'biological_memory_forest' };
              logger.debug(`🌲 Retrieved from biological memory forest: ${memoryId}`);
            }
          } catch (error) {
            logger.debug('⚠️ Not found in biological memory forest:', error instanceof Error ? error.message : String(error));
          }
        }

        // Try versioned memory forest if not found
        if (!foundMemory && this.versionedMemoryForest && this.versionedMemoryForest.retrieveMemory) {
          try {
            const memory = await this.versionedMemoryForest.retrieveMemory(memoryId);
            if (memory) {
              foundMemory = { ...memory, source: 'versioned_memory_forest' };
              logger.debug(`📚 Retrieved from versioned memory forest: ${memoryId}`);
            }
          } catch (error) {
            logger.debug('⚠️ Not found in versioned memory forest:', error instanceof Error ? error.message : String(error));
          }
        }

        // Try main memory forest if still not found
        if (!foundMemory && this.memoryForest && this.memoryForest.retrieveMemory) {
          try {
            const memory = await this.memoryForest.retrieveMemory(memoryId);
            if (memory) {
              foundMemory = { ...memory, source: 'main_memory_forest' };
              logger.debug(`🌳 Retrieved from main memory forest: ${memoryId}`);
            }
          } catch (error) {
            logger.debug('⚠️ Not found in main memory forest:', error instanceof Error ? error.message : String(error));
          }
        }
      }

      if (foundMemory) {
        logger.info(`✅ Memory retrieved: ${memoryId} from ${foundMemory.source}`);
        return foundMemory;
      } else {
        logger.warn(`⚠️ Memory not found in any forest system: ${memoryId}`);
        return null;
      }
    } catch (error) {
      logger.error('❌ Failed to retrieve memory:', error);
      throw error;
    }
  }

  async getStorageStats(): Promise<any> {
    try {
      const currentSize = await this.getCurrentStorageSize();
      const maxSize = this.config.maxStorageSize;

      // Get real stats from forest systems
      let biologicalStats = null;
      let versionedStats = null;
      let totalMemories = 0;

      if (this.biologicalMemoryForest && this.biologicalMemoryForest.getStatus) {
        try {
          biologicalStats = this.biologicalMemoryForest.getStatus();
          totalMemories += biologicalStats.totalNodes || 0;
        } catch (error) {
          logger.debug('⚠️ Could not get biological forest stats:', error);
        }
      }

      if (this.versionedMemoryForest && this.versionedMemoryForest.getStatus) {
        try {
          versionedStats = this.versionedMemoryForest.getStatus();
          totalMemories += versionedStats.totalVersions || 0;
        } catch (error) {
          logger.debug('⚠️ Could not get versioned forest stats:', error);
        }
      }

      return {
        currentSize,
        maxSize,
        usagePercentage: (currentSize / maxSize) * 100,
        decayActive: !!this.decayInterval,
        compressionActive: !!this.compressionInterval,
        compressionRatio: this.config.compressionRatio,
        totalMemories,
        lastDecayCycle: new Date().toISOString(),
        lastCompressionCycle: new Date().toISOString(),
        currentSeason: this.getCurrentSeason(),
        memoryForestHealth: {
          biologicalForest: !!this.biologicalMemoryForest,
          versionedForest: !!this.versionedMemoryForest,
          fractalOptimizer: !!this.fractalOptimizer
        },
        detailedStats: {
          biologicalForest: biologicalStats,
          versionedForest: versionedStats
        }
      };
    } catch (error) {
      logger.error('❌ Failed to get storage stats:', error);
      throw error;
    }
  }

  async shutdown(): Promise<void> {
    if (this.decayInterval) {
      clearInterval(this.decayInterval);
      this.decayInterval = null;
    }
    
    if (this.compressionInterval) {
      clearInterval(this.compressionInterval);
      this.compressionInterval = null;
    }

    this.initialized = false;
    logger.info('🌲 Advanced Memory Forest Integration shutdown complete');
  }
}

/**
 * Initialize Advanced Memory Forest Integration
 */
export async function initializeAdvancedMemoryForestIntegration(
  blackboard: any,
  memoryForest: any,
  spacetimeDB: any
): Promise<AdvancedMemoryForestIntegration | null> {
  try {
    const integration = new AdvancedMemoryForestIntegration(blackboard, memoryForest, spacetimeDB);
    const success = await integration.initialize();
    
    if (success) {
      return integration;
    } else {
      return null;
    }
  } catch (error) {
    logger.error('❌ Failed to initialize Advanced Memory Forest Integration:', error);
    return null;
  }
}

/**
 * Initialize Advanced Memory Forest Integration (wrapper for system manager)
 */
export async function initializeAdvancedMemoryForestIntegrationSystem(
  blackboard: any,
  memoryForest: any,
  spacetimeDB: any
): Promise<boolean> {
  try {
    const integration = await initializeAdvancedMemoryForestIntegration(blackboard, memoryForest, spacetimeDB);

    if (integration) {
      // Store globally for access
      (global as any).advancedMemoryForestIntegration = integration;
      return true;
    } else {
      return false;
    }
  } catch (error) {
    logger.error('❌ Failed to initialize Advanced Memory Forest Integration System:', error);
    return false;
  }
}

/**
 * Test Advanced Memory Forest Integration
 */
export async function testAdvancedMemoryForestIntegration(): Promise<boolean> {
  try {
    const integration = (global as any).advancedMemoryForestIntegration;

    if (!integration) {
      logger.warn('⚠️ Advanced Memory Forest Integration not found for testing');
      return false;
    }

    const status = integration.getStatus();

    logger.info('🧪 Testing Advanced Memory Forest Integration...');
    logger.info(`   - Initialized: ${status.initialized}`);
    logger.info(`   - Biological Memory Forest: ${status.biologicalMemoryForest}`);
    logger.info(`   - Versioned Memory Forest: ${status.versionedMemoryForest}`);
    logger.info(`   - Fractal Optimizer: ${status.fractalOptimizer}`);
    logger.info(`   - Decay Active: ${status.decayActive}`);
    logger.info(`   - Compression Active: ${status.compressionActive}`);
    logger.info(`   - Current Season: ${status.currentSeason}`);

    return status.initialized && (status.biologicalMemoryForest || status.versionedMemoryForest);
  } catch (error) {
    logger.error('❌ Advanced Memory Forest Integration test failed:', error);
    return false;
  }
}
