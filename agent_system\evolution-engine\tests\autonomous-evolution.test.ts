﻿/**
 * Autonomous Evolution System Integration Test
 * 
 * Tests the integration between all components of the autonomous evolution system.
 */

import { 
  AutonomousEvolutionManager,
  EvolutionScheduler,
  EvolutionFeedbackLoop
} from '../autonomous';
import { MetricsAPI } from '../metrics/api';
import { EvolutionEngine, EvolutionResult } from '../';
import { BlackboardInterface } from '@agent_system/blackboard/BlackboardInterface';
import { AgentType } from '../../../../../agents/agent-base/types';

describe('Autonomous Evolution System Integration', () => {
  let metricsAPI: MetricsAPI;
  let evolutionEngine: EvolutionEngine;
  let blackboard: BlackboardInterface;
  let autonomousEvolutionManager: AutonomousEvolutionManager;
  let evolutionScheduler: EvolutionScheduler;
  let evolutionFeedbackLoop: EvolutionFeedbackLoop;
  
  beforeEach(() => {
    // Create mock instances
    metricsAPI = new MetricsAPI();
    evolutionEngine = new EvolutionEngine();
    blackboard = new BlackboardInterface();
    
    // Mock evolution engine evolveAgent method
    jest.spyOn(evolutionEngine, 'evolveAgent').mockImplementation(async (plan) => {
      // Simulate a successful evolution
      const result: EvolutionResult = {
        success: true,
        message: 'Evolution completed successfully',
        changes: [
          {
            type: 'parameter',
            name: 'responseThreshold',
            oldValue: '0.5',
            newValue: '0.7'
          }
        ],
        metrics: [
          {
            name: plan.targetMetrics[0].name,
            before: plan.targetMetrics[0].currentValue,
            after: plan.targetMetrics[0].targetValue,
            improvement: plan.targetMetrics[0].targetValue - plan.targetMetrics[0].currentValue
          }
        ]
      };
      
      return result;
    });
    
    // Mock blackboard publish method
    jest.spyOn(blackboard, 'publish').mockImplementation((topic, data) => {
      // Do nothing
    });
    
    // Create autonomous evolution components
    autonomousEvolutionManager = new AutonomousEvolutionManager(
      metricsAPI,
      evolutionEngine,
      blackboard
    );
    
    evolutionScheduler = new EvolutionScheduler(
      autonomousEvolutionManager,
      blackboard
    );
    
    evolutionFeedbackLoop = new EvolutionFeedbackLoop(
      blackboard,
      metricsAPI
    );
  });
  
  afterEach(() => {
    // Clean up any test data
    jest.restoreAllMocks();
  });
  
  test('should trigger evolution based on metrics', async () => {
    // Record metrics that should trigger evolution
    const agentId = 'test-agent-1';
    const agentType = 'EnhancedTestAgent' as AgentType;
    
    // Add a test rule
    autonomousEvolutionManager.addRule({
      name: 'Test Rule',
      description: 'Test rule for triggering evolution',
      condition: {
        metricName: 'success_rate',
        operator: 'less_than',
        threshold: 0.7,
        duration: 0 // No duration for testing
      },
      agentTypes: [agentType],
      priority: 'high',
      cooldownMs: 0, // No cooldown for testing
      enabled: true
    });
    
    // Record a metric that should trigger the rule
    metricsAPI.recordMetric(agentId, agentType, {
      name: 'success_rate',
      value: 0.5, // Below threshold
      description: 'Success rate of operations',
      metricType: 'ratio',
      timestamp: Date.now()
    });
    
    // Manually trigger the rule check
    await autonomousEvolutionManager['checkEvolutionRules']();
    
    // Verify that evolveAgent was called
    expect(evolutionEngine.evolveAgent).toHaveBeenCalled();
    
    // Verify the evolution plan
    const plan = (evolutionEngine.evolveAgent as jest.Mock).mock.calls[0][0];
    expect(plan.agentId).toBe(agentId);
    expect(plan.agentType).toBe(agentType);
    expect(plan.targetMetrics[0].name).toBe('success_rate');
    expect(plan.targetMetrics[0].currentValue).toBeCloseTo(0.5);
    expect(plan.targetMetrics[0].targetValue).toBeGreaterThan(0.7);
  });
  
  test('should schedule and process evolutions', async () => {
    // Schedule an evolution
    const agentId = 'test-agent-2';
    const agentType = 'EnhancedTestAgent' as AgentType;
    
    const evolutionId = evolutionScheduler.scheduleEvolution(
      agentId,
      agentType,
      'success_rate',
      0.9,
      new Date(), // Schedule for now
      'high'
    );
    
    // Verify the evolution was scheduled
    const scheduledEvolutions = evolutionScheduler.getScheduledEvolutions();
    expect(scheduledEvolutions.length).toBe(1);
    expect(scheduledEvolutions[0].id).toBe(evolutionId);
    expect(scheduledEvolutions[0].agentId).toBe(agentId);
    expect(scheduledEvolutions[0].agentType).toBe(agentType);
    expect(scheduledEvolutions[0].metricName).toBe('success_rate');
    expect(scheduledEvolutions[0].targetValue).toBeCloseTo(0.9);
    expect(scheduledEvolutions[0].status).toBe('scheduled');
    
    // Process the schedule
    await evolutionScheduler['processSchedule']();
    
    // Verify that evolveAgent was called
    expect(evolutionEngine.evolveAgent).toHaveBeenCalled();
    
    // Verify the evolution plan
    const plan = (evolutionEngine.evolveAgent as jest.Mock).mock.calls[0][0];
    expect(plan.agentId).toBe(agentId);
    expect(plan.agentType).toBe(agentType);
    expect(plan.targetMetrics[0].name).toBe('success_rate');
    expect(plan.targetMetrics[0].targetValue).toBeCloseTo(0.9);
    
    // Verify the evolution was removed from the schedule
    const updatedScheduledEvolutions = evolutionScheduler.getScheduledEvolutions();
    expect(updatedScheduledEvolutions.length).toBe(0);
  });
  
  test('should generate feedback for evolution results', async () => {
    // Mock blackboard subscribe method
    const subscribeSpy = jest.spyOn(blackboard, 'subscribe').mockImplementation((topic, callback) => {
      // Store the callback for later use
      if (topic === 'evolution.completed') {
        completedCallback = callback;
      }
    });
    
    // Initialize the feedback loop to register the subscribers
    evolutionFeedbackLoop = new EvolutionFeedbackLoop(
      blackboard,
      metricsAPI
    );
    
    // Verify that subscribe was called
    expect(subscribeSpy).toHaveBeenCalledWith('evolution.completed', expect.any(Function));
    
    // Get the callback
    let completedCallback: ((data: any) => void) | null = null;
    
    // Mock blackboard publish method to capture feedback
    const publishSpy = jest.spyOn(blackboard, 'publish').mockImplementation((topic, data) => {
      if (topic === 'evolution.feedback') {
        capturedFeedback = data;
      }
    });
    
    let capturedFeedback: any = null;
    
    // Simulate an evolution completed event
    const evolutionResult: EvolutionResult = {
      success: true,
      message: 'Evolution completed successfully',
      changes: [
        {
          type: 'parameter',
          name: 'responseThreshold',
          oldValue: '0.5',
          newValue: '0.7'
        }
      ],
      metrics: [
        {
          name: 'success_rate',
          before: 0.5,
          after: 0.8,
          improvement: 0.3
        }
      ]
    };
    
    if (completedCallback) {
      completedCallback({
        agentId: 'test-agent-3',
        agentType: 'EnhancedTestAgent',
        timestamp: Date.now(),
        success: true,
        result: evolutionResult
      });
    }
    
    // Verify that feedback was published
    expect(publishSpy).toHaveBeenCalledWith('evolution.feedback', expect.any(Object));
    expect(capturedFeedback).not.toBeNull();
    expect(capturedFeedback.agentId).toBe('test-agent-3');
    expect(capturedFeedback.feedback).toBeDefined();
    expect(capturedFeedback.feedback.type).toBe('success');
    expect(capturedFeedback.feedback.suggestions.length).toBeGreaterThan(0);
  });
  
  test('should manually trigger evolution', async () => {
    // Manually trigger an evolution
    const agentId = 'test-agent-4';
    const agentType = 'EnhancedTestAgent' as AgentType;
    
    const result = await autonomousEvolutionManager.triggerEvolution(
      agentId,
      agentType,
      'success_rate',
      0.9,
      'high'
    );
    
    // Verify that evolveAgent was called
    expect(evolutionEngine.evolveAgent).toHaveBeenCalled();
    
    // Verify the evolution plan
    const plan = (evolutionEngine.evolveAgent as jest.Mock).mock.calls[0][0];
    expect(plan.agentId).toBe(agentId);
    expect(plan.agentType).toBe(agentType);
    expect(plan.targetMetrics[0].name).toBe('success_rate');
    expect(plan.targetMetrics[0].targetValue).toBeCloseTo(0.9);
    
    // Verify the result
    expect(result).not.toBeNull();
    expect(result?.success).toBe(true);
    expect(result?.message).toBe('Evolution completed successfully');
    expect(result?.changes.length).toBe(1);
    expect(result?.metrics.length).toBe(1);
  });
  
  test('should handle evolution failures', async () => {
    // Mock evolution engine to simulate a failure
    jest.spyOn(evolutionEngine, 'evolveAgent').mockImplementation(async (plan) => {
      // Simulate a failed evolution
      const result: EvolutionResult = {
        success: false,
        message: 'Evolution failed: timeout exceeded',
        changes: [],
        metrics: []
      };
      
      return result;
    });
    
    // Manually trigger an evolution
    const agentId = 'test-agent-5';
    const agentType = 'EnhancedTestAgent' as AgentType;
    
    const result = await autonomousEvolutionManager.triggerEvolution(
      agentId,
      agentType,
      'success_rate',
      0.9,
      'high'
    );
    
    // Verify that evolveAgent was called
    expect(evolutionEngine.evolveAgent).toHaveBeenCalled();
    
    // Verify the result
    expect(result).not.toBeNull();
    expect(result?.success).toBe(false);
    expect(result?.message).toContain('timeout');
    
    // Mock blackboard subscribe method
    const subscribeSpy = jest.spyOn(blackboard, 'subscribe').mockImplementation((topic, callback) => {
      // Store the callback for later use
      if (topic === 'evolution.failed') {
        failedCallback = callback;
      }
    });
    
    // Initialize the feedback loop to register the subscribers
    evolutionFeedbackLoop = new EvolutionFeedbackLoop(
      blackboard,
      metricsAPI
    );
    
    // Verify that subscribe was called
    expect(subscribeSpy).toHaveBeenCalledWith('evolution.failed', expect.any(Function));
    
    // Get the callback
    let failedCallback: ((data: any) => void) | null = null;
    
    // Mock blackboard publish method to capture feedback
    const publishSpy = jest.spyOn(blackboard, 'publish').mockImplementation((topic, data) => {
      if (topic === 'evolution.feedback') {
        capturedFeedback = data;
      }
    });
    
    let capturedFeedback: any = null;
    
    // Simulate an evolution failed event
    if (failedCallback) {
      failedCallback({
        agentId: 'test-agent-5',
        agentType: 'EnhancedTestAgent',
        timestamp: Date.now(),
        error: 'Evolution failed: timeout exceeded',
        result: {
          success: false,
          message: 'Evolution failed: timeout exceeded',
          changes: [],
          metrics: []
        }
      });
    }
    
    // Verify that feedback was published
    expect(publishSpy).toHaveBeenCalledWith('evolution.feedback', expect.any(Object));
    expect(capturedFeedback).not.toBeNull();
    expect(capturedFeedback.agentId).toBe('test-agent-5');
    expect(capturedFeedback.feedback).toBeDefined();
    expect(capturedFeedback.feedback.type).toBe('error');
    expect(capturedFeedback.feedback.suggestions.length).toBeGreaterThan(0);
  });
});

