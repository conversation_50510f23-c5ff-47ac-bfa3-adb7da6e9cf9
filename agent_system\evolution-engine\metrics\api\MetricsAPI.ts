/**
 * Metrics API
 *
 * Provides an API for accessing metrics data.
 */

import { MetricsAggregator } from '../MetricsAggregator';
import {
  AggregatedMetrics,
  PerformanceMetric,
  PerformanceAnomaly,
  ForecastResult,
  ClusterResult,
  PatternResult,
  StatisticalTestResult,
  Alert,
  AlertRule,
  MetricTypeDefinition,
  CustomAggregationFunction
} from '../types';
import { AgentType } from '../../../../../agents/agent-base/types';

export class MetricsAPI {
  private metricsAggregator: MetricsAggregator;

  constructor() {
    this.metricsAggregator = new MetricsAggregator();

    // Initialize REST API server
    this.metricsAggregator.initializeRestApiServer(this);
  }

  /**
   * Get aggregated metrics
   */
  public getAggregatedMetrics(
    timeRange?: { start: Date; end: Date }
  ): AggregatedMetrics {
    return this.metricsAggregator.getAggregatedMetrics(timeRange);
  }

  /**
   * Get metrics for a specific agent
   */
  public getAgentMetrics(
    agentId: string,
    timeRange?: { start: Date; end: Date }
  ): PerformanceMetric[] {
    return this.metricsAggregator.getAgentMetrics(agentId, timeRange);
  }

  /**
   * Get metrics for a specific agent type
   */
  public getAgentTypeMetrics(
    agentType: AgentType,
    timeRange?: { start: Date; end: Date }
  ): PerformanceMetric[] {
    return this.metricsAggregator.getAgentTypeMetrics(agentType, timeRange);
  }

  /**
   * Get metrics for a specific metric name
   */
  public getMetricsByName(
    metricName: string,
    timeRange?: { start: Date; end: Date }
  ): PerformanceMetric[] {
    return this.metricsAggregator.getMetricsByName(metricName, timeRange);
  }

  /**
   * Get performance trends
   */
  public getPerformanceTrends(
    timeRange?: { start: Date; end: Date },
    interval?: 'hour' | 'day' | 'week'
  ): Record<string, { timestamp: Date; value: number }[]> {
    return this.metricsAggregator.getPerformanceTrends(timeRange, interval);
  }

  /**
   * Get correlation between two metrics
   */
  public getMetricCorrelation(
    metric1: string,
    metric2: string,
    timeRange?: { start: Date; end: Date }
  ): number | null {
    return this.metricsAggregator.getMetricCorrelation(metric1, metric2, timeRange);
  }

  /**
   * Get evolution impact analysis
   */
  public getEvolutionImpact(
    timeRange?: { start: Date; end: Date }
  ): Record<string, { before: number; after: number; improvement: number }> {
    return this.metricsAggregator.getEvolutionImpact(timeRange);
  }

  /**
   * Get performance anomalies
   */
  public getPerformanceAnomalies(
    timeRange?: { start: Date; end: Date }
  ): { agentId: string; metricName: string; value: number; expected: number; timestamp: Date }[] {
    return this.metricsAggregator.getPerformanceAnomalies(timeRange);
  }

  /**
   * Get agents with metrics (for compatibility)
   */
  public getAgentsWithMetrics(
    timeRange?: { start: Date; end: Date }
  ): Array<{ agentId: string; agentType: AgentType; metrics: PerformanceMetric[] }> {
    const aggregatedMetrics = this.metricsAggregator.getAggregatedMetrics(timeRange);

    // Use the aggregated metrics which already have agent information
    return Object.entries(aggregatedMetrics.agentSummaries).map(([agentId, summary]) => ({
      agentId,
      agentType: summary.agentType,
      metrics: this.metricsAggregator.getAgentMetrics(agentId, timeRange)
    }));
  }

  /**
   * Get optimization suggestions
   */
  public getOptimizationSuggestions(): {
    agentId: string;
    suggestion: string;
    impact: number;
    confidence: number;
  }[] {
    return this.metricsAggregator.getOptimizationSuggestions();
  }

  /**
   * Record a metric
   */
  public recordMetric(
    agentId: string,
    agentType: AgentType,
    metric: PerformanceMetric
  ): void {
    this.metricsAggregator.recordMetric(agentId, agentType, metric);
  }

  /**
   * Advanced Analytics Methods
   */

  /**
   * Forecast future values for a metric
   */
  public forecastMetric(
    metricName: string,
    historyDays: number = 7,
    forecastDays: number = 3,
    interval: 'hour' | 'day' = 'day'
  ): ForecastResult {
    return this.metricsAggregator.forecastMetric(metricName, historyDays, forecastDays, interval);
  }

  /**
   * Cluster agents by performance patterns
   */
  public clusterAgentsByPerformance(
    metricNames: string[] = ['overall_performance', 'success_rate', 'response_time'],
    clusterCount: number = 3,
    timeRange?: { start: Date; end: Date }
  ): ClusterResult {
    return this.metricsAggregator.clusterAgentsByPerformance(metricNames, clusterCount, timeRange);
  }

  /**
   * Perform statistical significance testing for metric comparisons
   */
  public testStatisticalSignificance(
    metric1: string,
    metric2: string,
    timeRange?: { start: Date; end: Date }
  ): StatisticalTestResult {
    return this.metricsAggregator.testStatisticalSignificance(metric1, metric2, timeRange);
  }

  /**
   * Detect patterns in metrics using machine learning
   */
  public detectPatterns(
    metricName: string,
    timeRange?: { start: Date; end: Date },
    interval: 'hour' | 'day' = 'day'
  ): PatternResult {
    return this.metricsAggregator.detectPatterns(metricName, timeRange, interval);
  }

  /**
   * Alert System Methods
   */

  /**
   * Get all active alerts
   */
  public getActiveAlerts(): Alert[] {
    return this.metricsAggregator.getActiveAlerts();
  }

  /**
   * Get alert history
   */
  public getAlertHistory(limit: number = 100): Alert[] {
    return this.metricsAggregator.getAlertHistory(limit);
  }

  /**
   * Get alerts for a specific agent
   */
  public getAlertsForAgent(agentId: string): Alert[] {
    return this.metricsAggregator.getAlertsForAgent(agentId);
  }

  /**
   * Acknowledge an alert
   */
  public acknowledgeAlert(alertId: string, acknowledgedBy: string): boolean {
    return this.metricsAggregator.acknowledgeAlert(alertId, acknowledgedBy);
  }

  /**
   * Get all alert rules
   */
  public getAlertRules(): AlertRule[] {
    return this.metricsAggregator.getAlertRules();
  }

  /**
   * Add a new alert rule
   */
  public addAlertRule(rule: Omit<AlertRule, 'id' | 'createdAt' | 'updatedAt'>): AlertRule {
    return this.metricsAggregator.addAlertRule(rule);
  }

  /**
   * Update an existing alert rule
   */
  public updateAlertRule(ruleId: string, updates: Partial<AlertRule>): boolean {
    return this.metricsAggregator.updateAlertRule(ruleId, updates);
  }

  /**
   * Delete an alert rule
   */
  public deleteAlertRule(ruleId: string): boolean {
    return this.metricsAggregator.deleteAlertRule(ruleId);
  }

  /**
   * Set the alert check interval
   */
  public setAlertCheckInterval(intervalMs: number): void {
    this.metricsAggregator.setAlertCheckInterval(intervalMs);
  }

  /**
   * Custom Metrics Methods
   */

  /**
   * Register a custom metric type
   */
  public registerMetricType(definition: MetricTypeDefinition): boolean {
    return this.metricsAggregator.registerMetricType(definition);
  }

  /**
   * Get a metric type definition
   */
  public getMetricType(name: string): MetricTypeDefinition | undefined {
    return this.metricsAggregator.getMetricType(name);
  }

  /**
   * Get all metric types
   */
  public getAllMetricTypes(): MetricTypeDefinition[] {
    return this.metricsAggregator.getAllMetricTypes();
  }

  /**
   * Register a custom aggregation function
   */
  public registerAggregation(aggregation: CustomAggregationFunction): boolean {
    return this.metricsAggregator.registerAggregation(aggregation);
  }

  /**
   * Get an aggregation function
   */
  public getAggregation(name: string): CustomAggregationFunction | undefined {
    return this.metricsAggregator.getAggregation(name);
  }

  /**
   * Get all aggregation functions
   */
  public getAllAggregations(): CustomAggregationFunction[] {
    return this.metricsAggregator.getAllAggregations();
  }

  /**
   * Get aggregation functions compatible with a metric type
   */
  public getCompatibleAggregations(metricType: string): CustomAggregationFunction[] {
    return this.metricsAggregator.getCompatibleAggregations(metricType);
  }

  /**
   * Aggregate metrics using a specific aggregation function
   */
  public aggregateMetrics(metrics: PerformanceMetric[], aggregationName: string): number | null {
    return this.metricsAggregator.aggregateMetricsWithFunction(metrics, aggregationName);
  }

  /**
   * Validate a metric against its type definition
   */
  public validateMetric(metric: PerformanceMetric): { valid: boolean; errors: string[] } {
    return this.metricsAggregator.validateMetric(metric);
  }

  /**
   * Format a metric value according to its type
   */
  public formatMetricValue(typeName: string, value: any): string {
    return this.metricsAggregator.formatMetricValue(typeName, value);
  }

  /**
   * External Integration Methods
   */

  /**
   * Start the Prometheus exporter
   */
  public startPrometheusExporter(port: number = 9090, prefix: string = 'alice_'): void {
    this.metricsAggregator.startPrometheusExporter(port, prefix);
  }

  /**
   * Stop the Prometheus exporter
   */
  public stopPrometheusExporter(): void {
    this.metricsAggregator.stopPrometheusExporter();
  }

  /**
   * Start the REST API server
   */
  public startRestApiServer(port: number = 8080, apiKey: string | null = null): void {
    this.metricsAggregator.startRestApiServer(port, apiKey);
  }

  /**
   * Stop the REST API server
   */
  public stopRestApiServer(): void {
    this.metricsAggregator.stopRestApiServer();
  }

  /**
   * Register a webhook
   */
  public registerWebhook(
    id: string,
    url: string,
    headers: Record<string, string> = {},
    metricFilter?: (metric: PerformanceMetric) => boolean
  ): void {
    this.metricsAggregator.registerWebhook(id, url, headers, metricFilter);
  }

  /**
   * Unregister a webhook
   */
  public unregisterWebhook(id: string): boolean {
    return this.metricsAggregator.unregisterWebhook(id);
  }

  /**
   * Get all webhooks
   */
  public getWebhooks(): Record<string, {
    url: string;
    enabled: boolean;
  }> {
    return this.metricsAggregator.getWebhooks();
  }

  /**
   * Configure the Grafana exporter
   */
  public configureGrafanaExporter(
    grafanaUrl: string,
    apiKey: string,
    dashboardUid: string = '',
    exportIntervalMs: number = 60000
  ): void {
    this.metricsAggregator.configureGrafanaExporter(grafanaUrl, apiKey, dashboardUid, exportIntervalMs);
  }

  /**
   * Start the Grafana exporter
   */
  public startGrafanaExporter(): void {
    this.metricsAggregator.startGrafanaExporter();
  }

  /**
   * Stop the Grafana exporter
   */
  public stopGrafanaExporter(): void {
    this.metricsAggregator.stopGrafanaExporter();
  }
}
