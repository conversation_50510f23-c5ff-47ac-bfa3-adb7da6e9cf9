/**
 * Request Analyzer for Alice AGI
 * 
 * Analyzes incoming user requests to determine the appropriate processing strategy,
 * required systems, and complexity level for intelligent routing through the 7-stage pipeline.
 */

export interface RequestAnalysis {
  requestType: string;
  complexity: 'simple' | 'complex';
  processingStrategy: string;
  requiredSystems: string[];
  requiresCode: boolean;
  requiresCreativity: boolean;
  requiresAnalysis: boolean;
  estimatedProcessingTime: number;
  priority: 'low' | 'medium' | 'high';
}

/**
 * Analyze a user request to determine processing requirements
 */
export function analyzeRequestType(message: string): RequestAnalysis {
  const lowerMessage = message.toLowerCase();
  
  // Determine request type based on content analysis
  let requestType = 'general_conversation';
  let complexity: 'simple' | 'complex' = 'simple';
  let processingStrategy = 'standard';
  let requiredSystems: string[] = [];
  let requiresCode = false;
  let requiresCreativity = false;
  let requiresAnalysis = false;
  let estimatedProcessingTime = 2000; // Base time in ms
  let priority: 'low' | 'medium' | 'high' = 'medium';

  // Browser control detection
  if (lowerMessage.includes('open') && (lowerMessage.includes('browser') || lowerMessage.includes('google') || lowerMessage.includes('youtube') || lowerMessage.includes('github') || lowerMessage.includes('navigate'))) {
    requestType = 'browser_control';
    complexity = 'simple';
    processingStrategy = 'browser_automation';
    requiredSystems = ['selfAwarenessMonitor', 'mcpPlaywright'];
    estimatedProcessingTime = 3000;
    priority = 'high';
  }
  
  // Code-related requests
  else if (/code|program|function|class|script|implement|build|create.*app|develop/i.test(message)) {
    requestType = 'technical_analysis';
    complexity = 'complex';
    processingStrategy = 'code_generation';
    requiredSystems = ['autonomousCodeAgents', 'codeWritingAgent', 'securitySafetyFramework'];
    requiresCode = true;
    estimatedProcessingTime = 8000;
    priority = 'high';
  }
  
  // Creative requests
  else if (/create|design|imagine|story|art|dream|civilization|species|creative/i.test(message)) {
    requestType = 'creative_generation';
    complexity = 'complex';
    processingStrategy = 'creative_synthesis';
    requiredSystems = ['creativeGenerativeEngine', 'dreamCivilizationSimulator', 'realitySynthesisEngine'];
    requiresCreativity = true;
    estimatedProcessingTime = 6000;
    priority = 'high';
  }
  
  // Consciousness and self-awareness requests
  else if (/consciousness|aware|think|mind|self|identity|who.*you|what.*you/i.test(message)) {
    requestType = 'consciousness_analysis';
    complexity = 'complex';
    processingStrategy = 'consciousness_synthesis';
    requiredSystems = ['consciousnessModel', 'globalWorkspaceConsciousness', 'quantumConsciousnessAmplifier'];
    requiresAnalysis = true;
    estimatedProcessingTime = 5000;
    priority = 'high';
  }
  
  // Learning and evolution requests
  else if (/learn|evolve|improve|adapt|remember|memory|experience/i.test(message)) {
    requestType = 'learning_evolution';
    complexity = 'complex';
    processingStrategy = 'adaptive_learning';
    requiredSystems = ['autonomousEvolutionSystem', 'memorySystem', 'hyperMind'];
    requiresAnalysis = true;
    estimatedProcessingTime = 4000;
    priority = 'high';
  }
  
  // Analysis and explanation requests
  else if (/analyze|explain|understand|why|how|what.*mean|describe|tell.*about/i.test(message)) {
    requestType = 'analytical_processing';
    complexity = message.length > 100 ? 'complex' : 'simple';
    processingStrategy = 'analytical_synthesis';
    requiredSystems = ['consciousnessModel', 'memorySystem'];
    requiresAnalysis = true;
    estimatedProcessingTime = complexity === 'complex' ? 4000 : 2500;
    priority = 'medium';
  }
  
  // System status and demonstration requests
  else if (/demonstrate|show|test|status|system|processing|pipeline|agent/i.test(message)) {
    requestType = 'system_demonstration';
    complexity = 'simple';
    processingStrategy = 'system_showcase';
    requiredSystems = ['biologicalLLM', 'memorySystem'];
    requiresAnalysis = true;
    estimatedProcessingTime = 3000;
    priority = 'medium';
  }
  
  // Simple conversation
  else if (message.length < 50 && /hello|hi|hey|thanks|thank you|ok|yes|no/i.test(message)) {
    requestType = 'simple_conversation';
    complexity = 'simple';
    processingStrategy = 'conversational';
    requiredSystems = ['biologicalLLM'];
    estimatedProcessingTime = 1500;
    priority = 'low';
  }
  
  // Complex conversation (longer messages)
  else if (message.length > 100) {
    requestType = 'complex_conversation';
    complexity = 'complex';
    processingStrategy = 'comprehensive_analysis';
    requiredSystems = ['biologicalLLM', 'consciousnessModel', 'memorySystem'];
    requiresAnalysis = true;
    estimatedProcessingTime = 4000;
    priority = 'medium';
  }

  // Adjust complexity based on message length and content
  if (message.length > 200) {
    complexity = 'complex';
    estimatedProcessingTime += 2000;
  }

  // Set boolean flags based on content
  requiresCode = requiresCode || /code|program|function|class|script|implement|build.*app/i.test(message);
  requiresCreativity = requiresCreativity || /create|design|imagine|story|art|dream|creative/i.test(message);
  requiresAnalysis = requiresAnalysis || /analyze|explain|understand|why|how|describe|tell.*about/i.test(message);

  return {
    requestType,
    complexity,
    processingStrategy,
    requiredSystems,
    requiresCode,
    requiresCreativity,
    requiresAnalysis,
    estimatedProcessingTime,
    priority
  };
}

/**
 * Get processing stage requirements based on request analysis
 */
export function getStageRequirements(analysis: RequestAnalysis): { [stageName: string]: boolean } {
  return {
    'Goal Interpretation': true, // Always required
    'Dream Simulation': analysis.requiresCreativity || analysis.complexity === 'complex',
    'Society Activation': analysis.requiredSystems.length > 1 || analysis.complexity === 'complex',
    'Code Writing & Integration': analysis.requiresCode,
    'Cognitive Safeguards': analysis.complexity === 'complex' || analysis.requiresCode,
    'Deployment & Visualization': analysis.requiresCode || analysis.requiresCreativity || analysis.requestType === 'browser_control',
    'Self-Reflection & Evolution': true // Always required
  };
}

/**
 * Get estimated stage complexity for timing
 */
export function getStageComplexity(stageName: string, analysis: RequestAnalysis): number {
  const baseComplexity = analysis.complexity === 'complex' ? 3 : 1;
  
  switch (stageName) {
    case 'Goal Interpretation':
      return baseComplexity * 1;
    case 'Dream Simulation':
      return baseComplexity * (analysis.requiresCreativity ? 4 : 2);
    case 'Society Activation':
      return baseComplexity * 2;
    case 'Code Writing & Integration':
      return baseComplexity * (analysis.requiresCode ? 5 : 1);
    case 'Cognitive Safeguards':
      return baseComplexity * 2;
    case 'Deployment & Visualization':
      return baseComplexity * (analysis.requestType === 'browser_control' ? 2 : 3);
    case 'Self-Reflection & Evolution':
      return baseComplexity * 2;
    default:
      return baseComplexity;
  }
}

/**
 * Determine if a specific system is available
 */
export function isSystemAvailable(systemName: string): boolean {
  // Check if the system is available in the global scope
  return !!(global as any)[systemName];
}

/**
 * Get available systems from the required systems list
 */
export function getAvailableSystems(requiredSystems: string[]): string[] {
  return requiredSystems.filter(system => isSystemAvailable(system));
}
