import React, { useEffect, useState } from 'react';
import { BrowserRouter as Router, Routes, Route, useNavigate } from 'react-router-dom';
import { AnimatePresence, motion } from 'framer-motion';
import { useTheme } from './hooks/useTheme';
import { useMediaQuery } from './hooks/useMediaQuery';
import { useAuth } from './contexts/AuthContext';
import { useChat } from './contexts/ChatContext';
import { ErrorBoundary } from './components/common/ErrorBoundary';
import { Header } from './components/layout/Header';
import { Sidebar } from './components/layout/Sidebar';
import { ChatInterface } from './components/chat/ChatInterface';
import { DashboardPanel } from './components/dashboard/DashboardPanel';
import { SystemsIntegrationPanel } from './components/systems/SystemsIntegrationPanel';
import { GlobalFloatingBrowser } from './components/chat/GlobalFloatingBrowser';
import { monitoringService } from './services/monitoring';
import * as Sentry from '@sentry/react';
// import GodMode from './pages/GodMode';
// import AliceOS from './pages/AliceOS';
// import MMORPG from './pages/MMORPG';

const MainApp: React.FC = () => {
  const { theme, toggleTheme } = useTheme();
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  const { currentChat, selectChat, createNewChat, isLoading: chatLoading } = useChat();
  const isMobile = useMediaQuery('(max-width: 768px)');
  const [dashboardOpen, setDashboardOpen] = useState(false);
  const [systemsOpen, setSystemsOpen] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(!isMobile);
  const navigate = useNavigate();

  // Remove loading screen when React mounts and initialize monitoring
  useEffect(() => {
    console.log('Alice AGI UI mounting...');

    // Remove loading screen
    const loadingScreen = document.getElementById('loading-screen');
    if (loadingScreen) {
      console.log('Removing loading screen...');
      loadingScreen.style.display = 'none';
    }

    // Track app initialization
    monitoringService.trackAliceEvent('app_initialized', {
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight,
      },
      theme: theme,
      isMobile: isMobile,
    });

    // Set user context in Sentry
    if (user) {
      Sentry.setUser({
        id: user.id,
        username: user.username,
        email: user.email,
      });
    }

    // Track performance metrics after mount
    setTimeout(() => {
      const metrics = monitoringService.getPerformanceMetrics();
      if (metrics) {
        monitoringService.trackAliceEvent('performance_metrics', metrics);
      }
    }, 1000);

    // Cleanup monitoring on unmount
    return () => {
      monitoringService.cleanup();
    };
  }, [theme, isMobile, user]);

  const toggleSidebar = () => setSidebarOpen(prev => !prev);
  const toggleDashboard = () => setDashboardOpen(prev => !prev);
  const toggleSystems = () => setSystemsOpen(prev => !prev);

  // Show loading screen while auth or chat is loading
  if (authLoading || chatLoading) {
    return (
      <div className="h-screen flex items-center justify-center bg-light-background-primary dark:bg-dark-background-primary">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-light-text-secondary dark:text-dark-text-secondary">
            Initializing Alice AGI...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col bg-light-background-primary dark:bg-dark-background-primary text-light-text-primary dark:text-dark-text-primary">
      {/* Header */}
      <Header
        toggleSidebar={toggleSidebar}
        toggleTheme={toggleTheme}
        toggleDashboard={toggleDashboard}
        dashboardOpen={dashboardOpen}
        theme={theme}
        onGodModeClick={() => console.log('GodMode navigation (pages coming soon!)')}
        onSystemsClick={toggleSystems}
        systemsOpen={systemsOpen}
      />

      <div className="flex flex-1 overflow-hidden">
        {/* Sidebar */}
        <AnimatePresence>
          {sidebarOpen && (
            <motion.div
              initial={{ width: 0, opacity: 0 }}
              animate={{ width: isMobile ? '100%' : '320px', opacity: 1 }}
              exit={{ width: 0, opacity: 0 }}
              transition={{ duration: 0.3 }}
              className={`${isMobile ? 'fixed inset-0 z-40' : 'relative'} border-r border-light-border dark:border-dark-border`}
            >
              <Sidebar
                onClose={() => isMobile && setSidebarOpen(false)}
              />
            </motion.div>
          )}
        </AnimatePresence>

        {/* Main content */}
        <main className="flex-1 flex overflow-hidden">
          <div className={`flex-1 flex flex-col ${dashboardOpen ? 'lg:mr-[400px]' : ''} ${systemsOpen ? 'lg:ml-[500px]' : ''} transition-all duration-300`}>
            <ErrorBoundary>
              <ChatInterface />
            </ErrorBoundary>
          </div>

          {/* Dashboard panel */}
          <AnimatePresence>
            {dashboardOpen && (
              <motion.div
                initial={{ width: 0, opacity: 0 }}
                animate={{ width: '400px', opacity: 1 }}
                exit={{ width: 0, opacity: 0 }}
                transition={{ duration: 0.3 }}
                className="fixed right-0 top-16 bottom-0 w-[400px] border-l border-light-border dark:border-dark-border bg-light-background-primary dark:bg-dark-background-primary z-30 overflow-hidden"
              >
                <DashboardPanel onClose={() => setDashboardOpen(false)} />
              </motion.div>
            )}
          </AnimatePresence>

          {/* Systems Integration panel */}
          <AnimatePresence>
            {systemsOpen && (
              <motion.div
                initial={{ width: 0, opacity: 0 }}
                animate={{ width: '500px', opacity: 1 }}
                exit={{ width: 0, opacity: 0 }}
                transition={{ duration: 0.3 }}
                className="fixed left-0 top-16 bottom-0 w-[500px] border-r border-light-border dark:border-dark-border bg-light-background-primary dark:bg-dark-background-primary z-30 overflow-hidden"
              >
                <SystemsIntegrationPanel onClose={() => setSystemsOpen(false)} />
              </motion.div>
            )}
          </AnimatePresence>
        </main>
      </div>

      {/* Global Floating Browser */}
      <GlobalFloatingBrowser />
    </div>
  );
};

const App: React.FC = () => {
  return (
    <ErrorBoundary>
      <Router>
        <Routes>
          <Route path="/" element={<MainApp />} />
          {/* Pages coming soon! */}
          {/* <Route path="/godmode" element={<GodMode />} /> */}
          {/* <Route path="/aliceos" element={<AliceOS />} /> */}
          {/* <Route path="/mmorpg" element={<MMORPG />} /> */}
        </Routes>
      </Router>
    </ErrorBoundary>
  );
};

export default App;
