{"name": "arbitrage-ai", "version": "0.1.0", "private": true, "description": "Autonomous arbitrage AI system for finding, buying, and reselling underpriced products", "scripts": {"dev": "vite", "build": "tsc", "test": "vitest run", "test:watch": "vitest", "test:coverage": "jest --coverage", "test:memory": "jest tests/memory", "test:dream": "jest tests/dream", "test:evolution": "jest tests/evolution", "test:ancestry": "jest tests/ancestry", "test:ui": "jest tests/ui", "test:distributed": "jest tests/distributed", "test:fine-structure": "jest tests/constants tests/consciousness", "test:end-to-end": "jest tests/end-to-end", "test:simple-js": "node tests/simple-test.js", "test:memory-js": "node tests/memory-test.mjs", "test:mvcc-js": "node tests/mvcc-test.mjs", "test:forest-js": "node tests/forest-test.mjs", "test:all": "npm run test:alice", "build:watch": "tsc --watch", "build:vite": "tsc && vite build", "preview": "vite preview", "test:e2e": "playwright test", "test:evolution-integration": "ts-node scripts/test-evolution-integration.ts", "test:biological-integration": "ts-node scripts/test-biological-integration.ts", "test:dna-evolution": "ts-node scripts/test-dna-evolution.ts", "test:memory-forest": "ts-node scripts/test-memory-forest.ts", "test:alice-spine": "ts-node scripts/test-alice-spine.ts", "test:immune-system": "ts-node scripts/test-immune-system.ts", "test:viral-ecology": "ts-node tests/viral-ecology-test.ts", "test:dream-system": "ts-node scripts/test-dream-system.ts", "test:alicenet-integration": "ts-node scripts/test-alicenet-integration.ts", "test:browser-agent": "ts-node agents/specialized/browser/tests/run-tests.ts", "test:browser-agent:amazon": "ts-node agents/specialized/browser/tests/run-amazon-test.ts", "test:browser-agent:simple": "ts-node agents/specialized/browser/tests/run-simple-test.ts", "test:browser-agent:simple-js": "node agents/specialized/browser/tests/simple-browser-test.js", "test:browser-agent:evolution": "ts-node agent_system/evolution-engine/tests/browser-agent-evolution-test.ts", "test:browser-agent:evolution-simple": "ts-node agent_system/evolution-engine/tests/browser-agent-evolution-test-simple.ts", "test:browser-agent:evolution-js": "node agent_system/evolution-engine/tests/browser-agent-evolution-test.js", "test:browser-agent:message-handler": "ts-node agents/specialized/browser/tests/message-handler-test.ts", "test:browser-agent:memory-utils": "ts-node agents/specialized/browser/tests/memory-utils-test.ts", "test:browser-agent:integrated-system": "ts-node agents/specialized/browser/tests/integrated-system-test.ts", "test:browser-agent:integrated-system-js": "node agents/specialized/browser/tests/integrated-system-test.js", "test:model-orchestration": "ts-node tests/model-orchestration-test.ts", "test:cognitive-ml": "ts-node tests/cognitive-ml-test.ts", "test:shared-meta-history": "ts-node tests/shared-meta-history-test.ts", "test:cognitive-llm": "ts-node tests/cognitive-llm-test.ts", "test:sandman-mode": "ts-node tests/sandman-mode-test.ts", "test:timeline-fork-manager": "ts-node tests/timeline-fork-manager-test.ts", "test:hypermind": "ts-node tests/hypermind-test.ts", "test:aliceos-integration": "ts-node tests/aliceos-integration-test.ts", "test:arbitrage-system": "ts-node tests/arbitrage-system-test.ts", "test:marketplace-strategy": "ts-node tests/marketplace-strategy-test.ts", "test:autonomous-operation": "ts-node tests/autonomous-operation-test.ts", "test:self-evolution": "ts-node tests/self-evolution-test.ts", "test:real-comprehensive": "npm run build && node --experimental-modules dist/tests/enhanced-agi-test/real-comprehensive-test.js", "test:real-comprehensive:short": "npm run build && node --experimental-modules dist/tests/enhanced-agi-test/run-real-comprehensive-test.js -s -v", "test:real-comprehensive:long": "npm run build && node --experimental-modules dist/tests/enhanced-agi-test/run-real-comprehensive-test.js -d 12 -v", "test:real-comprehensive:ts": "ts-node --esm --transpile-only tests/enhanced-agi-test/run-real-comprehensive-test.js -s -v", "test:neural-agi": "node --experimental-modules tests/enhanced-agi-test/run-neural-agi-test.js", "test:neural-agi:short": "node --experimental-modules tests/enhanced-agi-test/run-neural-agi-test.js -s -v", "test:neural-agi:long": "node --experimental-modules tests/enhanced-agi-test/run-neural-agi-test.js -d 12 -v", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "launch:full": "node launchers/full_app_launcher/index.js", "launch:agent-test": "node launchers/agent-test-launcher/index.js", "launch:mock-data": "node launchers/mock-data-launcher/index.js", "launch:rl-sim": "node launchers/rl-simulation-launcher/index.js", "launch:blackout": "node launchers/blackout-mode-launcher/index.js", "launch:biological-dashboard": "ts-node scripts/run-biological-dashboard.ts", "launch:advanced-systems": "node launchers/advanced-systems-launcher/index.js", "launch:marketplace-ml": "node launchers/marketplace-ml-launcher/index.js", "launch:nlp-system": "node launchers/nlp-system-launcher/index.js", "launch:browser-automation": "node launchers/browser-automation-launcher/index.js", "launch:identity-management": "node launchers/identity-management-launcher/index.js", "launch:automated-arbitrage": "node launchers/automated-arbitrage-launcher/index.js", "launch:biological-arbitrage": "node launchers/biological-arbitrage-launcher/index.js", "optimize": "ts-node scripts/optimize-aliceos.ts", "optimize:no-benchmarks": "ts-node scripts/optimize-aliceos.ts --no-benchmarks", "test:integrated-biological-agi:1h": "ts-node scripts/test-biological-integration.ts --duration=3600", "test:integrated-biological-agi:12h": "ts-node scripts/test-biological-integration.ts --duration=43200", "test:model-orchestration:1h": "ts-node tests/model-orchestration-test.ts --duration=3600", "test:arbitrage-suite": "npm run test:arbitrage-system && npm run test:marketplace-strategy", "test:autonomous-suite": "npm run test:autonomous-operation && npm run test:self-evolution", "test:marketplace-data-ml": "ts-node tests/marketplace-data-ml-test.ts", "test:nlp-system": "ts-node tests/nlp-system-test.ts", "test:advanced-browser": "ts-node tests/advanced-browser-automation-test.ts", "test:identity-management": "ts-node tests/identity-management-test.ts", "test:automated-arbitrage": "ts-node tests/automated-arbitrage-execution-test.ts", "test:biological-arbitrage": "ts-node tests/biological-arbitrage-integration-test.ts", "test:marketplace-expansion": "ts-node tests/marketplace-expansion-test.ts", "test:aliceos-full": "npm run test:aliceos-integration && npm run test:arbitrage-suite && npm run test:autonomous-suite && npm run test:marketplace-data-ml && npm run test:nlp-system && npm run test:advanced-browser && npm run test:identity-management", "test:alice-agi": "node tests/run-complete-alice-agi-test.js", "test:alice-agi:verbose": "node tests/run-complete-alice-agi-test.js --verbose", "setup": "npm install && npm run build"}, "dependencies": {"@anthropic-ai/sdk": "^0.10.0", "@chakra-ui/icons": "^2.2.4", "@chakra-ui/react": "^3.17.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.0.1", "@mui/icons-material": "^7.1.0", "@mui/lab": "^7.0.0-beta.12", "@mui/material": "^7.1.0", "@mui/x-data-grid": "^8.3.0", "@mui/x-date-pickers": "^7.29.3", "@radix-ui/react-accordion": "^1.2.9", "@radix-ui/react-checkbox": "^1.3.0", "@radix-ui/react-dialog": "^1.1.12", "@radix-ui/react-dropdown-menu": "^2.1.13", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-radio-group": "^1.3.5", "@radix-ui/react-scroll-area": "^1.2.7", "@radix-ui/react-slider": "^1.3.3", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-toast": "^1.2.12", "@tensorflow/tfjs": "^4.11.0", "@tensorflow/tfjs-node": "^4.11.0", "@types/imap": "^0.8.42", "@types/jsonwebtoken": "^9.0.9", "@types/nodemailer": "^6.4.17", "@types/react-syntax-highlighter": "^15.5.13", "axios": "^1.9.0", "bcrypt": "^6.0.0", "body-parser": "^2.2.0", "chart.js": "^4.4.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cors": "^2.8.5", "d3": "^7.8.5", "date-fns": "^2.30.0", "diff": "^7.0.0", "dotenv": "^16.3.1", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "framer-motion": "^12.11.3", "helmet": "^8.1.0", "imap": "^0.8.19", "inversify": "^7.5.2", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "lucide-react": "^0.507.0", "mailparser": "^3.7.3", "mermaid": "^11.6.0", "multer": "^1.4.5-lts.2", "nodemailer": "^7.0.3", "openai": "^4.12.1", "playwright": "^1.52.0", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.56.2", "react-markdown": "^10.1.0", "react-router-dom": "^6.30.0", "react-syntax-highlighter": "^15.6.1", "recharts": "^2.15.3", "reflect-metadata": "^0.2.2", "remark-gfm": "^4.0.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "tailwind-merge": "^3.2.0", "tailwindcss": "^3.3.3", "three": "^0.155.0", "uuid": "^9.0.1", "vis-network": "^9.1.6", "vm2": "^3.9.19", "zod": "^3.24.4", "zustand": "^4.4.3"}, "devDependencies": {"@babel/cli": "^7.27.2", "@babel/core": "^7.27.1", "@babel/plugin-transform-modules-commonjs": "^7.27.1", "@babel/preset-env": "^7.27.2", "@babel/preset-typescript": "^7.27.1", "@playwright/test": "^1.38.1", "@types/bcrypt": "^5.0.2", "@types/chai": "^5.2.2", "@types/d3": "^7.4.1", "@types/express-rate-limit": "^5.1.3", "@types/helmet": "^0.0.48", "@types/jest": "^29.5.14", "@types/js-yaml": "^4.0.9", "@types/lodash": "^4.14.199", "@types/mocha": "^10.0.10", "@types/node": "^20.17.46", "@types/react": "^18.2.28", "@types/react-dom": "^18.2.13", "@types/socket.io-client": "^1.4.36", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "@types/three": "^0.155.0", "@types/uuid": "^9.0.8", "@types/ws": "^8.18.1", "@typescript-eslint/eslint-plugin": "^6.7.5", "@typescript-eslint/parser": "^6.7.5", "@vitejs/plugin-react": "^4.1.0", "autoprefixer": "^10.4.16", "chai": "^5.2.0", "eslint": "^8.51.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "jest": "^29.7.0", "mocha": "^11.2.2", "postcss": "^8.4.31", "prettier": "^3.0.3", "ts-jest": "^29.3.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3", "vite": "^6.3.4", "vitest": "^3.1.2", "webpack-cli": "^6.0.1"}, "main": "build_and_launch_enhanced_editor.js", "type": "module", "directories": {"doc": "docs", "example": "examples", "test": "tests"}, "keywords": [], "author": "", "license": "ISC"}