import React, { useState, useRef, useEffect } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { ChatInput } from './ChatInput';
import { ChatMessage } from './ChatMessage';
import { ChatWelcome } from './ChatWelcome';
import { ChatProcessingIndicator } from './ChatProcessingIndicator';
import { GlobalFloatingBrowser } from './GlobalFloatingBrowser';
import { useToast } from '../../hooks/useToast';
import { useAuth } from '../../contexts/AuthContext';
import { useChat } from '../../contexts/ChatContext';
import { chatApi } from '../../services/api';
import { chatSocket, useSocket } from '../../services/socket';
import { useSentry, useSentryComponentTracking } from '../../hooks/useSentry';

export const ChatInterface: React.FC = () => {
  const { addToast } = useToast();
  const { user } = useAuth();
  const { currentChat, addMessage, updateMessage, createNewChat } = useChat();
  const { isConnected } = useSocket();
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentStage, setCurrentStage] = useState<string | null>(null);
  const [progress, setProgress] = useState(0);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Sentry integration for error tracking and performance monitoring
  const {
    captureError,
    trackChatEvent,
    trackAliceInteraction,
    measurePerformance
  } = useSentry();

  // Track component lifecycle
  useSentryComponentTracking('ChatInterface');

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [currentChat?.messages]);

  // Set up socket event listeners
  useEffect(() => {
    if (!isConnected || !currentChat) return;

    // Handle processing stage updates
    const removeProcessingStageUpdate = chatSocket.on('processing_stage_update', (data) => {
      if (data.chatId === currentChat.id) {
        setCurrentStage(data.stage);
        setProgress(data.progress);

        // Update the last assistant message with the response
        if (data.response) {
          const lastMessage = currentChat.messages[currentChat.messages.length - 1];
          if (lastMessage && lastMessage.role === 'assistant' && lastMessage.status === 'loading') {
            updateMessage(currentChat.id, lastMessage.id, {
              content: data.response,
              status: 'complete',
            });
          }
        }
      }
    });

    // Handle message response
    const removeMessageResponse = chatSocket.onMessageResponse((data) => {
      if (data.chatId === currentChat.id) {
        setIsProcessing(false);
        setCurrentStage(null);
        setProgress(0);

        // Update the last assistant message with the response
        const lastMessage = currentChat.messages[currentChat.messages.length - 1];
        if (lastMessage && lastMessage.role === 'assistant' && lastMessage.status === 'loading') {
          updateMessage(currentChat.id, lastMessage.id, {
            content: data.response,
            status: 'complete',
            browserAction: data.browserAction, // Include browser action data
          });
        }

        addToast({
          type: 'success',
          title: 'Response generated',
          message: 'Alice has completed processing your request',
          duration: 3000,
        });
      }
    });

    // Handle processing completed
    const removeProcessingCompleted = chatSocket.onProcessingCompleted((data) => {
      if (data.chatId === currentChat.id) {
        setIsProcessing(false);
        setCurrentStage(null);
        setProgress(0);
      }
    });

    // Handle processing error
    const removeProcessingError = chatSocket.onProcessingError((error) => {
      console.error('Processing error received from socket:', error);

      // Track error in Sentry
      captureError(new Error(`Chat processing error: ${error.details || 'Unknown error'}`), {
        chatId: currentChat?.id,
        errorType: 'socket_processing_error',
        errorDetails: error.details,
        currentStage,
        progress,
      });

      setIsProcessing(false);
      setCurrentStage(null);
      setProgress(0);

      const errorDetails = error.details || 'An error occurred while processing your request';

      // Track chat error event
      trackChatEvent('message_received', {
        status: 'error',
        errorDetails,
        chatId: currentChat?.id,
      });

      addToast({
        type: 'error',
        title: 'Processing error',
        message: errorDetails,
        duration: 5000,
      });

      // Update the last assistant message with error
      const lastMessage = currentChat.messages[currentChat.messages.length - 1];
      if (lastMessage && lastMessage.role === 'assistant' && lastMessage.status === 'loading') {
        updateMessage(currentChat.id, lastMessage.id, {
          content: `I apologize, but I encountered an error while processing your request: ${errorDetails}. Please try again.`,
          status: 'error',
        });
      } else {
        // Add a system error message
        addMessage(currentChat.id, {
          role: 'system',
          content: `Error: ${errorDetails}. Please try again or refresh the page.`,
          status: 'error',
        });
      }
    });

    // Clean up event listeners
    return () => {
      removeProcessingStageUpdate();
      removeMessageResponse();
      removeProcessingCompleted();
      removeProcessingError();
    };
  }, [isConnected, currentChat, addToast, updateMessage, addMessage]);

  // Handle sending a message
  const handleSendMessage = async (content: string) => {
    if (!content.trim() || !user) return;

    // Track message sending event
    trackChatEvent('message_sent', {
      messageLength: content.length,
      chatId: currentChat?.id,
      userId: user.id,
      hasExistingChat: !!currentChat,
    });

    try {
      let chatToUse = currentChat;

      // Create a new chat if we don't have one
      if (!chatToUse) {
        chatToUse = createNewChat();
        trackChatEvent('chat_created', {
          userId: user.id,
          chatId: chatToUse.id,
        });
      }

      // Add user message
      addMessage(chatToUse.id, {
        role: 'user',
        content,
      });

      // Add assistant loading message
      const assistantMessage = addMessage(chatToUse.id, {
        role: 'assistant',
        content: '',
        status: 'loading',
      });

      setIsProcessing(true);

      // Send message via socket or API
      if (isConnected) {
        await chatSocket.sendMessage(chatToUse.id, content, user.id);
      } else {
        // Fallback to REST API if socket is not connected
        try {
          const response = await chatApi.sendMessage(chatToUse.id, content, user.id);
          
          // Update assistant message with response
          updateMessage(chatToUse.id, assistantMessage.id, {
            content: response.data.content,
            status: 'complete',
          });

          setIsProcessing(false);
        } catch (apiError) {
          console.error('API error:', apiError);

          // Track API error in Sentry
          captureError(apiError as Error, {
            chatId: chatToUse.id,
            messageId: assistantMessage.id,
            errorType: 'api_fallback_error',
            isConnected,
          });

          updateMessage(chatToUse.id, assistantMessage.id, {
            content: 'I apologize, but I encountered an error while processing your request. Please try again.',
            status: 'error',
          });

          setIsProcessing(false);

          addToast({
            type: 'error',
            title: 'Error',
            message: 'Failed to send message. Please try again.',
            duration: 5000,
          });
        }
      }
    } catch (error) {
      console.error('Error sending message:', error);

      // Track general error in Sentry
      captureError(error as Error, {
        chatId: currentChat?.id,
        userId: user.id,
        errorType: 'message_send_error',
        messageContent: content.substring(0, 100), // First 100 chars for context
        isConnected,
      });

      setIsProcessing(false);

      addToast({
        type: 'error',
        title: 'Error',
        message: 'Failed to send message. Please try again.',
        duration: 5000,
      });
    }
  };

  // Get current chat messages
  const currentMessages = currentChat?.messages || [];

  return (
    <div className="flex flex-col h-full">
      {/* Clean Messages Area */}
      <div className="flex-1 overflow-y-auto p-4 pb-32 md:pb-4">
        <div className="max-w-4xl mx-auto">
          {!currentChat || currentMessages.length === 0 ? (
            <ChatWelcome onSendMessage={handleSendMessage} />
          ) : (
            <>
              {currentMessages.map((message, index) => (
                <ChatMessage
                  key={message.id}
                  message={message}
                  isLastMessage={index === currentMessages.length - 1}
                  chatId={currentChat?.id}
                />
              ))}
            </>
          )}
          <div ref={messagesEndRef} />
        </div>
      </div>

      {/* Clean Input Area - Fixed z-index to not interfere with browser buttons */}
      <div className="absolute bottom-16 md:bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-light-background-primary dark:from-dark-background-primary to-transparent pt-16 pointer-events-none">
        <div className="max-w-4xl mx-auto pointer-events-auto">
          <ChatInput
            onSendMessage={handleSendMessage}
            isProcessing={isProcessing}
          />
        </div>
      </div>

      <AnimatePresence>
        {isProcessing && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            transition={{ duration: 0.2 }}
            className="fixed bottom-20 md:bottom-24 left-1/2 transform -translate-x-1/2 z-10"
          >
            <ChatProcessingIndicator
              currentStage={currentStage}
              progress={progress}
            />
          </motion.div>
        )}
      </AnimatePresence>

      {/* Global Floating Browser */}
      <GlobalFloatingBrowser />
    </div>
  );
};
