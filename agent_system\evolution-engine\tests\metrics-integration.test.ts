﻿/**
 * Metrics Aggregation System Integration Test
 * 
 * Tests the integration between all components of the metrics aggregation system.
 */

import { MetricsAPI } from '../metrics/api';
import { MetricTypeRegistry, CustomMetricAggregator } from '../metrics/custom';
import { AgentType } from '../../../../../agents/agent-base/types';
import { BlackboardInterface } from '@agent_system/blackboard/BlackboardInterface';

describe('Metrics Aggregation System Integration', () => {
  let metricsAPI: MetricsAPI;
  let metricTypeRegistry: MetricTypeRegistry;
  let customMetricAggregator: CustomMetricAggregator;
  
  beforeEach(() => {
    // Create a fresh instance for each test
    metricsAPI = new MetricsAPI();
    metricTypeRegistry = MetricTypeRegistry.getInstance();
    customMetricAggregator = CustomMetricAggregator.getInstance();
  });
  
  afterEach(() => {
    // Clean up any test data
    jest.restoreAllMocks();
  });
  
  test('should record and retrieve metrics', async () => {
    // Record some test metrics
    const agentId = 'test-agent-1';
    const agentType = 'EnhancedTestAgent' as AgentType;
    
    metricsAPI.recordMetric(agentId, agentType, {
      name: 'success_rate',
      value: 0.85,
      description: 'Success rate of operations',
      metricType: 'ratio',
      timestamp: Date.now()
    });
    
    metricsAPI.recordMetric(agentId, agentType, {
      name: 'response_time',
      value: 250,
      description: 'Response time for operations',
      metricType: 'time',
      timestamp: Date.now()
    });
    
    // Get aggregated metrics
    const aggregatedMetrics = metricsAPI.getAggregatedMetrics();
    
    // Verify the metrics were recorded
    expect(aggregatedMetrics.agentSummaries[agentId]).toBeDefined();
    expect(aggregatedMetrics.agentSummaries[agentId].agentType).toBe(agentType);
    expect(aggregatedMetrics.agentSummaries[agentId].latestMetrics.success_rate).toBeCloseTo(0.85);
    expect(aggregatedMetrics.agentSummaries[agentId].latestMetrics.response_time).toBeCloseTo(250);
  });
  
  test('should support custom metric types', async () => {
    // Register a custom metric type
    const registered = metricTypeRegistry.registerMetricType({
      name: 'currency',
      description: 'A monetary value',
      valueType: 'number',
      valueRange: [0, Infinity],
      defaultAggregation: 'sum',
      availableAggregations: ['sum', 'average', 'min', 'max'],
      formatValue: (value) => `$${value.toFixed(2)}`,
      validateValue: (value) => value >= 0
    });
    
    expect(registered).toBe(true);
    
    // Verify the custom metric type was registered
    const metricType = metricTypeRegistry.getMetricType('currency');
    expect(metricType).toBeDefined();
    expect(metricType?.name).toBe('currency');
    
    // Record a metric with the custom type
    const agentId = 'test-agent-2';
    const agentType = 'EnhancedTestAgent' as AgentType;
    
    metricsAPI.recordMetric(agentId, agentType, {
      name: 'revenue',
      value: 125.50,
      description: 'Revenue generated',
      metricType: 'currency',
      timestamp: Date.now()
    });
    
    // Get agent metrics
    const agentMetrics = metricsAPI.getAgentMetrics(agentId);
    
    // Verify the metric was recorded
    expect(agentMetrics.length).toBeGreaterThan(0);
    const revenueMetric = agentMetrics.find(m => m.name === 'revenue');
    expect(revenueMetric).toBeDefined();
    expect(revenueMetric?.value).toBeCloseTo(125.50);
    expect(revenueMetric?.metricType).toBe('currency');
    
    // Format the metric value
    const formattedValue = metricTypeRegistry.formatMetricValue('currency', 125.50);
    expect(formattedValue).toBe('$125.50');
  });
  
  test('should support custom aggregations', async () => {
    // Register a custom aggregation function
    const registered = customMetricAggregator.registerAggregation({
      name: 'weighted_average',
      description: 'Calculate the weighted average of values',
      compatibleTypes: ['ratio', 'score'],
      aggregateFunction: (metrics) => {
        if (metrics.length === 0) return null;
        
        let weightedSum = 0;
        let weightSum = 0;
        
        metrics.forEach((metric, index) => {
          const weight = index + 1; // Simple weight based on position
          weightedSum += metric.value * weight;
          weightSum += weight;
        });
        
        return weightedSum / weightSum;
      }
    });
    
    expect(registered).toBe(true);
    
    // Verify the custom aggregation was registered
    const aggregation = customMetricAggregator.getAggregation('weighted_average');
    expect(aggregation).toBeDefined();
    expect(aggregation?.name).toBe('weighted_average');
    
    // Record some metrics
    const agentId = 'test-agent-3';
    const agentType = 'EnhancedTestAgent' as AgentType;
    
    const metrics = [
      {
        name: 'accuracy',
        value: 0.7,
        description: 'Accuracy of operations',
        metricType: 'ratio',
        timestamp: new Date(Date.now() - 3000)
      },
      {
        name: 'accuracy',
        value: 0.8,
        description: 'Accuracy of operations',
        metricType: 'ratio',
        timestamp: new Date(Date.now() - 2000)
      },
      {
        name: 'accuracy',
        value: 0.9,
        description: 'Accuracy of operations',
        metricType: 'ratio',
        timestamp: new Date(Date.now() - 1000)
      }
    ];
    
    metrics.forEach(metric => {
      metricsAPI.recordMetric(agentId, agentType, metric);
    });
    
    // Get agent metrics
    const agentMetrics = metricsAPI.getAgentMetrics(agentId);
    
    // Filter accuracy metrics
    const accuracyMetrics = agentMetrics.filter(m => m.name === 'accuracy');
    expect(accuracyMetrics.length).toBe(3);
    
    // Aggregate using custom aggregation
    const weightedAverage = customMetricAggregator.aggregate(accuracyMetrics, 'weighted_average');
    
    // Expected: (0.7*1 + 0.8*2 + 0.9*3) / (1 + 2 + 3) = 0.833
    expect(weightedAverage).toBeCloseTo(0.833, 3);
  });
  
  test('should detect anomalies in metrics', async () => {
    // Record some normal metrics
    const agentId = 'test-agent-4';
    const agentType = 'EnhancedTestAgent' as AgentType;
    
    // Record 10 normal metrics around 0.8
    for (let i = 0; i < 10; i++) {
      metricsAPI.recordMetric(agentId, agentType, {
        name: 'success_rate',
        value: 0.8 + (Math.random() * 0.1 - 0.05), // 0.75 to 0.85
        description: 'Success rate of operations',
        metricType: 'ratio',
        timestamp: new Date(Date.now() - (10 - i) * 1000)
      });
    }
    
    // Record an anomalous metric
    metricsAPI.recordMetric(agentId, agentType, {
      name: 'success_rate',
      value: 0.4, // Significantly lower
      description: 'Success rate of operations',
      metricType: 'ratio',
      timestamp: Date.now()
    });
    
    // Get performance anomalies
    const anomalies = metricsAPI.getPerformanceAnomalies();
    
    // Verify the anomaly was detected
    expect(anomalies.length).toBeGreaterThan(0);
    const successRateAnomaly = anomalies.find(a => 
      a.agentId === agentId && a.metricName === 'success_rate'
    );
    expect(successRateAnomaly).toBeDefined();
    expect(successRateAnomaly?.value).toBeCloseTo(0.4);
  });
  
  test('should forecast future metric values', async () => {
    // Record historical metrics
    const agentId = 'test-agent-5';
    const agentType = 'EnhancedTestAgent' as AgentType;
    
    // Record metrics with an upward trend
    for (let i = 0; i < 7; i++) {
      metricsAPI.recordMetric(agentId, agentType, {
        name: 'success_rate',
        value: 0.7 + (i * 0.02), // 0.7, 0.72, 0.74, ..., 0.82
        description: 'Success rate of operations',
        metricType: 'ratio',
        timestamp: new Date(Date.now() - (7 - i) * 24 * 60 * 60 * 1000) // One per day
      });
    }
    
    // Forecast future values
    const forecast = metricsAPI.forecastMetric('success_rate', 7, 3, 'day');
    
    // Verify the forecast
    expect(forecast).toBeDefined();
    expect(forecast.metricName).toBe('success_rate');
    expect(forecast.historicalData.length).toBeGreaterThan(0);
    expect(forecast.forecastData.length).toBe(3);
    
    // The forecast should continue the upward trend
    const lastHistorical = forecast.historicalData[forecast.historicalData.length - 1].value;
    const firstForecast = forecast.forecastData[0].value;
    expect(firstForecast).toBeGreaterThan(lastHistorical);
  });
  
  test('should cluster agents by performance patterns', async () => {
    // Record metrics for multiple agents with different patterns
    const agentTypes = ['EnhancedTestAgent', 'EnhancedTestAgent2', 'EnhancedTestAgent3'] as AgentType[];
    
    // High performance agent
    for (let i = 0; i < 5; i++) {
      metricsAPI.recordMetric('high-perf-agent', agentTypes[0], {
        name: 'success_rate',
        value: 0.9 + (Math.random() * 0.05), // 0.9 to 0.95
        description: 'Success rate of operations',
        metricType: 'ratio',
        timestamp: new Date(Date.now() - (5 - i) * 1000)
      });
      
      metricsAPI.recordMetric('high-perf-agent', agentTypes[0], {
        name: 'response_time',
        value: 100 + (Math.random() * 50), // 100 to 150 ms
        description: 'Response time for operations',
        metricType: 'time',
        timestamp: new Date(Date.now() - (5 - i) * 1000)
      });
    }
    
    // Medium performance agent
    for (let i = 0; i < 5; i++) {
      metricsAPI.recordMetric('medium-perf-agent', agentTypes[1], {
        name: 'success_rate',
        value: 0.7 + (Math.random() * 0.1), // 0.7 to 0.8
        description: 'Success rate of operations',
        metricType: 'ratio',
        timestamp: new Date(Date.now() - (5 - i) * 1000)
      });
      
      metricsAPI.recordMetric('medium-perf-agent', agentTypes[1], {
        name: 'response_time',
        value: 300 + (Math.random() * 100), // 300 to 400 ms
        description: 'Response time for operations',
        metricType: 'time',
        timestamp: new Date(Date.now() - (5 - i) * 1000)
      });
    }
    
    // Low performance agent
    for (let i = 0; i < 5; i++) {
      metricsAPI.recordMetric('low-perf-agent', agentTypes[2], {
        name: 'success_rate',
        value: 0.5 + (Math.random() * 0.1), // 0.5 to 0.6
        description: 'Success rate of operations',
        metricType: 'ratio',
        timestamp: new Date(Date.now() - (5 - i) * 1000)
      });
      
      metricsAPI.recordMetric('low-perf-agent', agentTypes[2], {
        name: 'response_time',
        value: 500 + (Math.random() * 200), // 500 to 700 ms
        description: 'Response time for operations',
        metricType: 'time',
        timestamp: new Date(Date.now() - (5 - i) * 1000)
      });
    }
    
    // Cluster agents by performance
    const clusters = metricsAPI.clusterAgentsByPerformance(
      ['success_rate', 'response_time'],
      3
    );
    
    // Verify the clusters
    expect(clusters).toBeDefined();
    expect(clusters.clusters.length).toBe(3);
    expect(clusters.clusterCount).toBe(3);
    
    // Each agent should be in a different cluster
    const agentClusters = new Map<string, number>();
    
    clusters.clusters.forEach(cluster => {
      cluster.agentIds.forEach(agentId => {
        agentClusters.set(agentId, cluster.id);
      });
    });
    
    expect(agentClusters.size).toBe(3);
    expect(agentClusters.get('high-perf-agent')).not.toBe(agentClusters.get('medium-perf-agent'));
    expect(agentClusters.get('high-perf-agent')).not.toBe(agentClusters.get('low-perf-agent'));
    expect(agentClusters.get('medium-perf-agent')).not.toBe(agentClusters.get('low-perf-agent'));
  });
});

