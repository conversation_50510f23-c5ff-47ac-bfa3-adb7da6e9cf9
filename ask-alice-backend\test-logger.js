// Test logger functionality
console.log('🔧 Testing logger...');

try {
  const { logger } = require('./src/utils/logger');
  
  console.log('✅ Logger imported successfully');
  
  logger.info('🚀 Test info message');
  logger.debug('🔍 Test debug message');
  logger.warn('⚠️ Test warning message');
  logger.error('❌ Test error message');
  
  console.log('✅ Logger test completed');
} catch (error) {
  console.error('❌ Logger test failed:', error);
}
