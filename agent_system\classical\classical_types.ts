/**
 * Classical Types - Type definitions for classical computing systems
 */

// Hardware Types
export interface ClassicalProcessor {
  id: string;
  name: string;
  cores: number;
  threads: number;
  frequency: number; // GHz
  maxFrequency: number; // GHz
  cache: {
    l1: number; // KB
    l2: number; // KB
    l3: number; // MB
  };
  architecture: 'x86_64' | 'arm64' | 'risc-v' | 'mips';
  manufacturer: string;
  model: string;
  tdp: number; // Watts
}

export interface ClassicalMemory {
  total: number; // GB
  available: number; // GB
  used: number; // GB
  type: 'DDR3' | 'DDR4' | 'DDR5' | 'HBM' | 'GDDR6';
  speed: number; // MHz
  channels: number;
  latency: number; // nanoseconds
  bandwidth: number; // GB/s
}

export interface ClassicalStorage {
  id: string;
  type: 'SSD' | 'HDD' | 'NVMe' | 'eUFS' | 'Optane';
  capacity: number; // GB
  available: number; // GB
  speed: {
    read: number; // MB/s
    write: number; // MB/s
    random_read: number; // IOPS
    random_write: number; // IOPS
  };
  interface: 'SATA' | 'PCIe' | 'M.2' | 'U.2';
  endurance: number; // TBW (Terabytes Written)
}

export interface NetworkInterface {
  id: string;
  type: 'ethernet' | 'wifi' | 'bluetooth' | 'cellular';
  speed: number; // Mbps
  latency: number; // ms
  status: 'connected' | 'disconnected' | 'error';
  ipAddress?: string;
  macAddress: string;
}

// System Types
export interface SystemConfiguration {
  processor: ClassicalProcessor;
  memory: ClassicalMemory;
  storage: ClassicalStorage[];
  network: NetworkInterface[];
  os: {
    name: string;
    version: string;
    architecture: string;
    kernel: string;
  };
}

export interface SystemMetrics {
  cpu: {
    usage: number; // percentage
    temperature: number; // Celsius
    frequency: number; // GHz
    load: [number, number, number]; // 1min, 5min, 15min
  };
  memory: {
    usage: number; // percentage
    available: number; // GB
    cached: number; // GB
    buffers: number; // GB
  };
  storage: {
    usage: number; // percentage
    readRate: number; // MB/s
    writeRate: number; // MB/s
    iops: number;
  };
  network: {
    bytesIn: number;
    bytesOut: number;
    packetsIn: number;
    packetsOut: number;
    errors: number;
  };
}

// Computation Types
export interface ComputationTask {
  id: string;
  name: string;
  type: 'cpu' | 'memory' | 'io' | 'network' | 'gpu' | 'mixed';
  priority: 'low' | 'medium' | 'high' | 'critical' | 'realtime';
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  estimatedTime: number; // milliseconds
  actualTime?: number; // milliseconds
  resources: {
    cpu: number; // percentage
    memory: number; // MB
    storage: number; // MB
    network: number; // KB/s
  };
  dependencies?: string[]; // task IDs
  metadata?: Record<string, any>;
  createdAt: Date;
  startedAt?: Date;
  completedAt?: Date;
}

export interface ComputationResult {
  taskId: string;
  success: boolean;
  result?: any;
  error?: string;
  warnings?: string[];
  executionTime: number;
  resourceUsage: {
    cpu: number;
    memory: number;
    storage: number;
    network: number;
  };
  performance: {
    throughput?: number;
    latency?: number;
    efficiency?: number;
  };
  timestamp: Date;
}

export interface TaskScheduler {
  algorithm: 'fifo' | 'priority' | 'round_robin' | 'shortest_job' | 'fair_share';
  maxConcurrent: number;
  queueSize: number;
  preemption: boolean;
  affinityRules?: Record<string, number[]>; // task type -> CPU cores
}

// Algorithm Types
export interface Algorithm {
  id: string;
  name: string;
  type: 'sorting' | 'searching' | 'graph' | 'optimization' | 'ml' | 'crypto';
  complexity: {
    time: string; // Big O notation
    space: string; // Big O notation
  };
  parameters: Record<string, any>;
  implementation: 'native' | 'optimized' | 'parallel' | 'distributed';
}

export interface AlgorithmResult {
  algorithm: Algorithm;
  input: any;
  output: any;
  metrics: {
    executionTime: number;
    memoryUsage: number;
    iterations?: number;
    comparisons?: number;
    swaps?: number;
  };
  success: boolean;
  error?: string;
}

// Data Structures
export interface DataStructure {
  type: 'array' | 'list' | 'stack' | 'queue' | 'tree' | 'graph' | 'hash' | 'heap';
  size: number;
  capacity?: number;
  operations: {
    insert: string; // complexity
    delete: string; // complexity
    search: string; // complexity
    update: string; // complexity
  };
  memoryLayout: 'contiguous' | 'linked' | 'hybrid';
}

// Performance Types
export interface PerformanceProfile {
  name: string;
  cpu: {
    singleCore: number; // benchmark score
    multiCore: number; // benchmark score
    efficiency: number; // performance per watt
  };
  memory: {
    bandwidth: number; // GB/s
    latency: number; // ns
    throughput: number; // operations/s
  };
  storage: {
    sequential: number; // MB/s
    random: number; // IOPS
    latency: number; // ms
  };
  overall: number; // composite score
}

export interface BenchmarkResult {
  name: string;
  category: 'cpu' | 'memory' | 'storage' | 'network' | 'overall';
  score: number;
  unit: string;
  details: Record<string, number>;
  timestamp: Date;
  environment: {
    os: string;
    compiler?: string;
    optimization?: string;
  };
}

// Optimization Types
export interface OptimizationTarget {
  metric: 'performance' | 'power' | 'memory' | 'latency' | 'throughput';
  weight: number;
  constraint?: {
    min?: number;
    max?: number;
  };
}

export interface OptimizationStrategy {
  name: string;
  targets: OptimizationTarget[];
  techniques: string[];
  parameters: Record<string, any>;
  adaptive: boolean;
}

export interface OptimizationResult {
  strategy: OptimizationStrategy;
  before: SystemMetrics;
  after: SystemMetrics;
  improvement: Record<string, number>; // percentage improvements
  tradeoffs: Record<string, number>; // what was sacrificed
  stable: boolean;
  duration: number; // optimization time
}

// Parallel Computing Types
export interface ParallelTask {
  id: string;
  type: 'data_parallel' | 'task_parallel' | 'pipeline';
  workers: number;
  synchronization: 'barrier' | 'lock' | 'atomic' | 'lock_free';
  communication: 'shared_memory' | 'message_passing' | 'hybrid';
  loadBalancing: 'static' | 'dynamic' | 'work_stealing';
}

export interface ParallelResult {
  task: ParallelTask;
  speedup: number; // actual speedup achieved
  efficiency: number; // speedup / workers
  scalability: number; // how well it scales with workers
  overhead: number; // parallelization overhead
  bottlenecks: string[]; // identified bottlenecks
}

// Distributed Computing Types
export interface DistributedNode {
  id: string;
  address: string;
  capabilities: SystemConfiguration;
  status: 'online' | 'offline' | 'busy' | 'maintenance';
  load: number; // 0-1
  latency: number; // ms to this node
}

export interface DistributedTask {
  id: string;
  type: 'map_reduce' | 'master_worker' | 'peer_to_peer';
  nodes: string[]; // node IDs
  dataDistribution: 'replicated' | 'partitioned' | 'hybrid';
  faultTolerance: 'none' | 'checkpoint' | 'replication';
  consistency: 'strong' | 'eventual' | 'weak';
}

// Utility Types
export type CPUArchitecture = 'x86_64' | 'arm64' | 'risc-v' | 'mips';
export type MemoryType = 'DDR3' | 'DDR4' | 'DDR5' | 'HBM' | 'GDDR6';
export type StorageType = 'SSD' | 'HDD' | 'NVMe' | 'eUFS' | 'Optane';
export type TaskPriority = 'low' | 'medium' | 'high' | 'critical' | 'realtime';
export type TaskStatus = 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';

// Export all types as namespace
export namespace ClassicalTypes {
  export type Processor = ClassicalProcessor;
  export type Memory = ClassicalMemory;
  export type Storage = ClassicalStorage;
  export type Network = NetworkInterface;
  export type Configuration = SystemConfiguration;
  export type Metrics = SystemMetrics;
  export type Task = ComputationTask;
  export type Result = ComputationResult;
  export type Scheduler = TaskScheduler;
  export type Performance = PerformanceProfile;
  export type Benchmark = BenchmarkResult;
  export type Optimization = OptimizationStrategy;
  export type Parallel = ParallelTask;
  export type Distributed = DistributedTask;
}

export default ClassicalTypes;
