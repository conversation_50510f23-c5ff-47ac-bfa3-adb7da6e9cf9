/**
 * Quantum Types - Type definitions for quantum computing systems
 */

// Basic Quantum Types
export interface Complex {
  real: number;
  imaginary: number;
}

export interface QuantumState {
  qubits: number;
  amplitudes: Complex[];
  entangled: boolean;
  measured: boolean;
  coherenceTime?: number;
  fidelity?: number;
}

export interface Qubit {
  id: number;
  state: Complex[];
  entangled: boolean;
  entangledWith?: number[];
  coherenceTime: number;
  errorRate: number;
}

// Quantum Gates
export interface QuantumGate {
  name: string;
  matrix: Complex[][];
  qubits: number[];
  parameters?: number[];
  duration?: number;
}

export interface GateSequence {
  gates: QuantumGate[];
  totalDuration: number;
  fidelity: number;
}

// Quantum Circuits
export interface QuantumCircuit {
  id: string;
  name?: string;
  qubits: number;
  gates: QuantumGate[];
  measurements: number[];
  depth: number;
  width: number;
  fidelity?: number;
}

export interface CircuitOptimization {
  original: QuantumCircuit;
  optimized: QuantumCircuit;
  gateReduction: number;
  depthReduction: number;
  fidelityImprovement: number;
}

// Quantum Measurements
export interface QuantumMeasurement {
  qubit: number;
  result: 0 | 1;
  probability: number;
  timestamp: Date;
  basis?: 'computational' | 'hadamard' | 'custom';
}

export interface MeasurementResult {
  measurements: QuantumMeasurement[];
  bitstring: string;
  probability: number;
  shots: number;
  histogram: Record<string, number>;
}

// Quantum Algorithms
export interface QuantumAlgorithm {
  name: string;
  description: string;
  qubits: number;
  gates: number;
  depth: number;
  complexity: string;
  applications: string[];
}

export interface AlgorithmResult {
  algorithm: string;
  input: any;
  output: any;
  success: boolean;
  probability: number;
  iterations: number;
  executionTime: number;
}

// Quantum Error Correction
export interface QuantumError {
  type: 'bit_flip' | 'phase_flip' | 'depolarizing' | 'amplitude_damping';
  qubit: number;
  probability: number;
  timestamp: Date;
}

export interface ErrorCorrectionCode {
  name: string;
  logicalQubits: number;
  physicalQubits: number;
  distance: number;
  threshold: number;
  correctable: string[];
}

// Quantum Hardware
export interface QuantumProcessor {
  id: string;
  name: string;
  qubits: number;
  connectivity: number[][];
  gateSet: string[];
  coherenceTime: number;
  gateTime: number;
  errorRates: {
    single: number;
    two: number;
    readout: number;
  };
}

export interface QuantumDevice {
  processor: QuantumProcessor;
  calibration: Date;
  temperature: number;
  status: 'online' | 'offline' | 'maintenance';
  queue: number;
}

// Quantum Simulation
export interface SimulationConfig {
  backend: 'statevector' | 'density_matrix' | 'stabilizer';
  shots: number;
  noise: boolean;
  optimization: boolean;
  seed?: number;
}

export interface SimulationResult {
  config: SimulationConfig;
  circuit: QuantumCircuit;
  result: MeasurementResult;
  executionTime: number;
  memoryUsage: number;
}

// Quantum Networking
export interface QuantumChannel {
  id: string;
  source: string;
  target: string;
  fidelity: number;
  distance: number;
  protocol: 'bb84' | 'e91' | 'sarg04';
}

export interface QuantumNetwork {
  nodes: string[];
  channels: QuantumChannel[];
  topology: 'star' | 'mesh' | 'ring' | 'tree';
  security: 'quantum_key_distribution' | 'quantum_digital_signature';
}

// Quantum Machine Learning
export interface QuantumDataset {
  features: number[][];
  labels: number[];
  encoding: 'amplitude' | 'angle' | 'basis';
  qubits: number;
}

export interface QuantumModel {
  type: 'vqc' | 'qnn' | 'qsvm' | 'qaoa';
  parameters: number[];
  layers: number;
  entanglement: 'linear' | 'circular' | 'full';
  optimizer: 'adam' | 'cobyla' | 'spsa';
}

export interface QuantumTrainingResult {
  model: QuantumModel;
  accuracy: number;
  loss: number;
  iterations: number;
  convergence: boolean;
  quantumAdvantage: boolean;
}

// Quantum Optimization
export interface OptimizationProblem {
  type: 'maxcut' | 'tsp' | 'portfolio' | 'scheduling';
  variables: number;
  constraints: any[];
  objective: string;
}

export interface QAOAResult {
  problem: OptimizationProblem;
  solution: number[];
  energy: number;
  approximationRatio: number;
  layers: number;
  success: boolean;
}

// Quantum Chemistry
export interface Molecule {
  name: string;
  atoms: Array<{
    element: string;
    position: [number, number, number];
  }>;
  charge: number;
  multiplicity: number;
}

export interface QuantumChemistryResult {
  molecule: Molecule;
  groundStateEnergy: number;
  excitedStates: number[];
  dipole: [number, number, number];
  qubits: number;
  accuracy: number;
}

// Utility Types
export type QuantumBasis = 'computational' | 'hadamard' | 'bell' | 'ghz';
export type NoiseModel = 'ideal' | 'depolarizing' | 'amplitude_damping' | 'phase_damping';
export type QuantumBackend = 'simulator' | 'hardware' | 'cloud';

// Enums
export enum GateType {
  PAULI_X = 'X',
  PAULI_Y = 'Y',
  PAULI_Z = 'Z',
  HADAMARD = 'H',
  CNOT = 'CNOT',
  TOFFOLI = 'TOFFOLI',
  PHASE = 'P',
  T = 'T',
  S = 'S'
}

export enum AlgorithmType {
  GROVER = 'grover',
  SHOR = 'shor',
  DEUTSCH = 'deutsch',
  SIMON = 'simon',
  QAOA = 'qaoa',
  VQE = 'vqe',
  QFT = 'qft'
}

// Export all types as namespace
export namespace QuantumTypes {
  export type State = QuantumState;
  export type Gate = QuantumGate;
  export type Circuit = QuantumCircuit;
  export type Measurement = QuantumMeasurement;
  export type Algorithm = QuantumAlgorithm;
  export type Error = QuantumError;
  export type Processor = QuantumProcessor;
  export type Device = QuantumDevice;
  export type Channel = QuantumChannel;
  export type Network = QuantumNetwork;
  export type Dataset = QuantumDataset;
  export type Model = QuantumModel;
}

export default QuantumTypes;
