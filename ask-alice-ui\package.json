{"name": "ask-alice-ui", "version": "1.0.0", "description": "Ask Alice - Unified Life Architect Interface", "private": true, "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint src --ext ts,tsx", "format": "prettier --write \"src/**/*.{ts,tsx,css}\"", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@headlessui/react": "1.7.17", "@heroicons/react": "2.0.18", "@nivo/calendar": "^0.99.0", "@nivo/line": "^0.99.0", "@nivo/network": "^0.99.0", "@radix-ui/react-accordion": "1.1.2", "@radix-ui/react-avatar": "1.0.4", "@radix-ui/react-dialog": "1.0.5", "@radix-ui/react-dropdown-menu": "2.0.6", "@radix-ui/react-popover": "1.0.7", "@radix-ui/react-select": "1.2.2", "@radix-ui/react-slider": "1.1.2", "@radix-ui/react-tabs": "1.0.4", "@radix-ui/react-toast": "1.1.5", "@radix-ui/react-tooltip": "1.0.7", "@sentry/react": "^9.30.0", "@sentry/tracing": "^7.120.3", "@tailwindcss/aspect-ratio": "0.4.2", "@tailwindcss/container-queries": "0.1.1", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "4.35.3", "@tanstack/react-table": "8.10.7", "autoprefixer": "10.4.16", "axios": "1.6.2", "chart.js": "4.4.0", "d3": "7.8.5", "date-fns": "2.30.0", "framer-motion": "10.16.4", "highlight.js": "11.9.0", "immer": "10.0.3", "lodash": "4.17.21", "marked": "9.1.5", "mermaid": "^11.6.0", "postcss": "8.4.31", "postcss-selector-parser": "6.0.13", "react": "18.2.0", "react-chartjs-2": "5.2.0", "react-dom": "18.2.0", "react-feather": "^2.0.10", "react-force-graph": "^1.44.0", "react-force-graph-2d": "^1.27.1", "react-markdown": "8.0.7", "react-router-dom": "6.20.0", "react-syntax-highlighter": "15.5.0", "recharts": "2.9.0", "rehype-katex": "^7.0.1", "remark-gfm": "^3.0.1", "remark-math": "^6.0.0", "socket.io-client": "4.7.2", "source-map": "0.7.4", "source-map-js": "1.2.1", "tailwindcss": "3.3.5", "three": "0.158.0", "uuid": "9.0.1", "zustand": "4.4.6"}, "devDependencies": {"@storybook/addon-essentials": "7.5.3", "@storybook/addon-interactions": "7.5.3", "@storybook/addon-links": "7.5.3", "@storybook/blocks": "7.5.3", "@storybook/react": "7.5.3", "@storybook/react-vite": "7.5.3", "@storybook/test": "^7.5.3", "@testing-library/jest-dom": "6.1.4", "@testing-library/react": "14.1.2", "@testing-library/user-event": "14.5.1", "@types/d3": "7.4.3", "@types/lodash": "4.14.200", "@types/node": "20.10.0", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@types/react-syntax-highlighter": "15.5.10", "@types/three": "0.158.2", "@types/uuid": "9.0.7", "@typescript-eslint/eslint-plugin": "6.10.0", "@typescript-eslint/parser": "6.10.0", "@vitejs/plugin-react": "4.1.1", "eslint": "8.55.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-react": "7.33.2", "eslint-plugin-react-hooks": "4.6.0", "eslint-plugin-storybook": "^0.8.0", "jsdom": "22.1.0", "prettier": "3.1.0", "typescript": "5.3.3", "vite": "4.5.0", "vitest": "0.34.6"}}