import { Alice<PERSON><PERSON>wrightMCPServer } from './alice-playwright-mcp-server';
import { AliceFileSystemMCPServer } from './alice-filesystem-mcp-server';
import { AliceTerminalMCPServer } from './alice-terminal-mcp-server';
import { Alice<PERSON>ontextEngineMCPServer } from './alice-context-engine-mcp-server';
import { logger } from '../../../utils/logger';

/**
 * Alice Biological LLM + MCP Integration
 * Connects Alice's BiologicalLLM to MCP servers (like Playwright)
 * Provides the same code-based response capabilities that Augment Agent has
 */

interface MCPRequest {
  tool: string;
  params: any;
  context?: string;
  sessionId?: string;
}

interface MCPResponse {
  success: boolean;
  result?: any;
  error?: string;
  metadata: {
    tool: string;
    duration: number;
    timestamp: string;
  };
}

interface BiologicalLLMRequest {
  prompt: string;
  context?: any;
  tools?: string[];
  maxTokens?: number;
  temperature?: number;
}

export class AliceBiologicalMCPIntegration {
  private playwrightMCP: AlicePlaywrightMCPServer;
  private fileSystemMCP: AliceFileSystemMCPServer;
  private terminalMCP: AliceTerminalMCPServer;
  private contextEngineMCP: AliceContextEngineMCPServer;
  private biologicalLLM: any;
  private isInitialized: boolean = false;
  private activeRequests: Map<string, MCPRequest> = new Map();
  private allMCPTools: Map<string, any> = new Map();
  private registeredTools: any[] = [];
  private enhancedTools: any[] = [];

  constructor() {
    this.playwrightMCP = new AlicePlaywrightMCPServer();
    this.fileSystemMCP = new AliceFileSystemMCPServer();
    this.terminalMCP = new AliceTerminalMCPServer();
    this.contextEngineMCP = new AliceContextEngineMCPServer();
  }

  public async initialize(): Promise<void> {
    logger.info('🧬 Initializing Alice Biological LLM + ALL MCP Servers...');

    try {
      // Initialize ALL MCP Servers
      logger.info('🎭 Initializing Playwright MCP Server...');
      await this.playwrightMCP.initialize();

      logger.info('📁 Initializing File System MCP Server...');
      await this.fileSystemMCP.initialize();

      logger.info('🖥️ Initializing Terminal MCP Server...');
      await this.terminalMCP.initialize();

      logger.info('🧠 Initializing Context Engine MCP Server...');
      await this.contextEngineMCP.initialize();

      // Get BiologicalLLM from global context
      this.biologicalLLM = (global as any).biologicalLLM || (global as any).biologicalLLMSystem;

      if (!this.biologicalLLM) {
        logger.warn('⚠️ BiologicalLLM not found, creating enhanced mock instance');
        this.biologicalLLM = this.createEnhancedBiologicalLLM();
      }

      // Setup comprehensive tool integration
      await this.setupComprehensiveToolIntegration();

      // Connect BiologicalLLM to ALL MCP tools
      await this.connectBiologicalLLMToMCPTools();

      this.isInitialized = true;
      logger.info('✅ Alice Biological LLM + ALL MCP Servers initialized successfully!');

      // Log capabilities
      this.logMCPCapabilities();

    } catch (error) {
      logger.error('❌ Failed to initialize Biological MCP Integration:', error);
      throw error;
    }
  }

  /**
   * Main method for Alice to respond to code-based prompts (like Augment does)
   */
  public async respondToCodePrompt(prompt: string, context?: any): Promise<any> {
    logger.info(`🧠 Alice responding to code prompt: ${prompt.substring(0, 100)}...`);
    
    const startTime = Date.now();
    
    try {
      // Analyze prompt to determine if browser tools are needed
      const toolsNeeded = await this.analyzePromptForTools(prompt, context);

      // Check if this is a browser-opening request
      const shouldOpenBrowser = this.shouldOpenBrowser(prompt);

      // Prepare BiologicalLLM request with available tools
      const llmRequest: BiologicalLLMRequest = {
        prompt: this.enhancePromptWithToolContext(prompt, toolsNeeded),
        context: {
          ...context,
          availableTools: toolsNeeded,
          mcpCapabilities: this.playwrightMCP.getCapabilities(),
          shouldOpenBrowser,
          browserAction: shouldOpenBrowser ? this.determineBrowserAction(prompt) : null
        },
        tools: toolsNeeded,
        maxTokens: 4000,
        temperature: 0.1
      };
      
      // Get response from BiologicalLLM
      const llmResponse = await this.biologicalLLM.generateResponse(llmRequest);
      
      // Process any tool calls in the response
      const processedResponse = await this.processToolCalls(llmResponse, context);
      
      return {
        success: true,
        response: processedResponse.text,
        toolCalls: processedResponse.toolCalls,
        metadata: {
          duration: Date.now() - startTime,
          timestamp: new Date().toISOString(),
          toolsUsed: toolsNeeded,
          model: 'BiologicalLLM'
        }
      };
      
    } catch (error) {
      logger.error('❌ Code prompt response failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Response generation failed',
        metadata: {
          duration: Date.now() - startTime,
          timestamp: new Date().toISOString(),
          model: 'BiologicalLLM'
        }
      };
    }
  }

  /**
   * Execute MCP tool directly (like Augment's tool execution)
   */
  public async executeMCPTool(toolName: string, params: any, context?: any): Promise<MCPResponse> {
    logger.info(`🔧 Alice executing MCP tool: ${toolName}`);
    
    const startTime = Date.now();
    const requestId = `mcp_${Date.now()}`;
    
    try {
      const request: MCPRequest = {
        tool: toolName,
        params,
        context: context?.prompt || 'Direct tool execution',
        sessionId: requestId
      };
      
      this.activeRequests.set(requestId, request);
      
      // Execute tool using unified MCP tool execution
      const result = await this.executeMCPToolByName(toolName, params);
      
      this.activeRequests.delete(requestId);
      
      return {
        success: true,
        result,
        metadata: {
          tool: toolName,
          duration: Date.now() - startTime,
          timestamp: new Date().toISOString()
        }
      };
      
    } catch (error) {
      logger.error(`❌ MCP tool execution failed: ${toolName}`, error);
      this.activeRequests.delete(requestId);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Tool execution failed',
        metadata: {
          tool: toolName,
          duration: Date.now() - startTime,
          timestamp: new Date().toISOString()
        }
      };
    }
  }

  /**
   * Alice's browser control (like Augment's browser capabilities)
   */
  public async controlBrowser(action: string, params: any): Promise<any> {
    logger.info(`🌐 Alice controlling browser: ${action}`);
    
    const toolName = `browser_${action}`;
    return await this.executeMCPTool(toolName, params, { prompt: `Browser ${action}` });
  }

  // Private helper methods
  private async setupComprehensiveToolIntegration(): Promise<void> {
    logger.info('🔗 Setting up comprehensive tool integration...');

    // Collect ALL MCP tools from all servers
    const playwrightTools = this.playwrightMCP.getTools();
    const fileSystemTools = this.fileSystemMCP.getTools();
    const terminalTools = this.terminalMCP.getTools();
    const contextEngineTools = this.contextEngineMCP.getTools();

    // Combine all tools
    const allTools = [
      ...playwrightTools,
      ...fileSystemTools,
      ...terminalTools,
      ...contextEngineTools
    ];

    // Store tools by name for easy access
    allTools.forEach(tool => {
      this.allMCPTools.set(tool.name, tool);
    });

    logger.info(`✅ Collected ${allTools.length} MCP tools from all servers`);
    logger.info(`   🎭 Playwright: ${playwrightTools.length} tools`);
    logger.info(`   📁 File System: ${fileSystemTools.length} tools`);
    logger.info(`   🖥️ Terminal: ${terminalTools.length} tools`);
    logger.info(`   🧠 Context Engine: ${contextEngineTools.length} tools`);

    // Store integration in global context
    (global as any).aliceBiologicalMCPIntegration = this;
    (global as any).aliceAllMCPTools = allTools;
  }

  private async connectBiologicalLLMToMCPTools(): Promise<void> {
    logger.info('🔌 Connecting BiologicalLLM to ALL MCP tools...');

    const allTools = Array.from(this.allMCPTools.values());

    // Register tools with BiologicalLLM if it supports tool registration
    if (this.biologicalLLM.registerTools) {
      await this.biologicalLLM.registerTools(allTools);
      logger.info(`✅ Registered ${allTools.length} MCP tools with BiologicalLLM`);
    }

    // Enhance BiologicalLLM with tool-calling capabilities
    if (this.biologicalLLM.enhanceWithTools) {
      await this.biologicalLLM.enhanceWithTools(allTools);
      logger.info('✅ Enhanced BiologicalLLM with tool-calling capabilities');
    }

    // Store tool mapping for response processing
    this.biologicalLLM.mcpToolMapping = this.allMCPTools;

    logger.info('🎯 BiologicalLLM is now connected to ALL MCP tools!');
  }

  private logMCPCapabilities(): void {
    logger.info('📋 Alice MCP Capabilities Summary:');
    logger.info('   🎭 Browser Automation: Navigation, clicking, typing, screenshots, accessibility snapshots');
    logger.info('   📁 File Operations: Reading, writing, directory listing, file search, copying, moving');
    logger.info('   🖥️ Terminal Management: Command execution, process management, environment variables');
    logger.info('   🧠 Codebase Understanding: Symbol search, dependency analysis, pattern recognition');
    logger.info('   🔧 Total Tools Available: ' + this.allMCPTools.size);
  }

  private async analyzePromptForTools(prompt: string, context?: any): Promise<string[]> {
    const tools: string[] = [];
    const promptLower = prompt.toLowerCase();

    // Analyze prompt for browser-related keywords
    const browserKeywords = [
      'browser', 'navigate', 'click', 'type', 'screenshot', 'page', 'website',
      'url', 'web', 'form', 'button', 'input', 'element', 'tab', 'window',
      'open', 'visit', 'go to', 'google', 'youtube', 'github', 'search'
    ];

    if (browserKeywords.some(keyword => promptLower.includes(keyword))) {
      tools.push(
        'browser_navigate',
        'browser_snapshot',
        'browser_click',
        'browser_type',
        'browser_wait_for',
        'browser_take_screenshot',
        'browser_tab_list',
        'browser_tab_new'
      );
    }

    // Analyze prompt for file system keywords
    const fileKeywords = [
      'file', 'read', 'write', 'save', 'create', 'delete', 'copy', 'move',
      'directory', 'folder', 'path', 'content', 'text', 'code'
    ];

    if (fileKeywords.some(keyword => promptLower.includes(keyword))) {
      tools.push(
        'file_read',
        'file_write',
        'file_exists',
        'directory_list',
        'directory_create',
        'file_search',
        'file_stats'
      );
    }

    // Analyze prompt for terminal keywords
    const terminalKeywords = [
      'terminal', 'command', 'execute', 'run', 'shell', 'bash', 'cmd',
      'process', 'npm', 'node', 'git', 'install', 'build', 'test'
    ];

    if (terminalKeywords.some(keyword => promptLower.includes(keyword))) {
      tools.push(
        'terminal_execute',
        'terminal_execute_async',
        'terminal_list_processes',
        'terminal_get_session',
        'terminal_system_info'
      );
    }

    // Analyze prompt for codebase/context keywords
    const codebaseKeywords = [
      'search', 'find', 'symbol', 'function', 'class', 'codebase', 'analyze',
      'structure', 'dependency', 'import', 'export', 'pattern', 'context'
    ];

    if (codebaseKeywords.some(keyword => promptLower.includes(keyword))) {
      tools.push(
        'codebase_search',
        'codebase_analyze_file',
        'codebase_find_symbol',
        'codebase_get_dependencies',
        'codebase_get_structure',
        'codebase_get_context'
      );
    }

    // If no specific tools detected, provide a basic set for general assistance
    if (tools.length === 0) {
      tools.push(
        'browser_snapshot',
        'file_read',
        'terminal_system_info',
        'codebase_get_structure'
      );
    }

    return tools;
  }

  private enhancePromptWithToolContext(prompt: string, tools: string[]): string {
    if (tools.length === 0) {
      return prompt;
    }

    const toolContext = `
You are Alice, an advanced AI assistant with access to comprehensive MCP (Model Context Protocol) tools.

Available tools for this request:
${tools.map(tool => `- ${tool}: ${this.getToolDescription(tool)}`).join('\n')}

IMPORTANT: When you need to use a tool, format it as: [TOOL:tool_name]{"param1": "value1", "param2": "value2"}

You can:
- Browse and control web pages (navigate, click, type, screenshot)
- Read, write, and manage files and directories
- Execute terminal commands and manage processes
- Search and analyze the codebase for symbols, dependencies, and patterns

Respond naturally and use tools when appropriate to fulfill the request.

Original request: ${prompt}`;

    return toolContext;
  }

  private getToolDescription(toolName: string): string {
    const descriptions: { [key: string]: string } = {
      // Browser Tools
      'browser_navigate': 'Navigate to a URL',
      'browser_snapshot': 'Take accessibility snapshot of current page',
      'browser_click': 'Click on page elements',
      'browser_type': 'Type text into form fields',
      'browser_wait_for': 'Wait for conditions or time',
      'browser_take_screenshot': 'Capture page screenshots',
      'browser_tab_list': 'List open browser tabs',
      'browser_tab_new': 'Open new browser tab',

      // File System Tools
      'file_read': 'Read file contents',
      'file_write': 'Write content to file',
      'file_exists': 'Check if file exists',
      'file_delete': 'Delete file',
      'file_copy': 'Copy file to another location',
      'file_move': 'Move/rename file',
      'file_search': 'Search for files by pattern',
      'file_stats': 'Get file statistics',
      'directory_list': 'List directory contents',
      'directory_create': 'Create directory',

      // Terminal Tools
      'terminal_execute': 'Execute terminal command synchronously',
      'terminal_execute_async': 'Execute terminal command asynchronously',
      'terminal_list_processes': 'List running terminal processes',
      'terminal_kill_process': 'Kill terminal process',
      'terminal_get_session': 'Get terminal session status',
      'terminal_send_input': 'Send input to running terminal session',
      'terminal_system_info': 'Get system information',
      'terminal_get_env': 'Get environment variables',
      'terminal_set_env': 'Set environment variable',

      // Context Engine Tools
      'codebase_search': 'Search codebase for patterns, symbols, or content',
      'codebase_analyze_file': 'Analyze specific file for symbols and dependencies',
      'codebase_find_symbol': 'Find symbol definition and usage',
      'codebase_get_dependencies': 'Get file dependencies and imports',
      'codebase_get_structure': 'Get codebase structure and overview',
      'codebase_reindex': 'Reindex codebase for latest changes',
      'codebase_get_context': 'Get contextual information for code understanding',
      'codebase_find_patterns': 'Find code patterns and architectural insights'
    };

    return descriptions[toolName] || 'MCP tool';
  }

  private async processToolCalls(llmResponse: any, context?: any): Promise<any> {
    const toolCalls: any[] = [];
    let responseText = llmResponse.text || llmResponse.content || '';
    
    // Parse tool calls from LLM response (simplified parsing)
    const toolCallPattern = /\[TOOL:(\w+)\]\s*({[^}]+})/g;
    let match;
    
    while ((match = toolCallPattern.exec(responseText)) !== null) {
      const toolName = match[1];
      const params = JSON.parse(match[2]);
      
      try {
        const result = await this.executeMCPToolByName(toolName, params);
        toolCalls.push({
          tool: toolName,
          params,
          result,
          success: true
        });

        // Replace tool call with result in response
        const resultSummary = this.summarizeToolResult(toolName, result);
        responseText = responseText.replace(match[0], `[TOOL_RESULT:${toolName}] ${resultSummary}`);

      } catch (error) {
        logger.error(`❌ Tool call failed: ${toolName}`, error);
        toolCalls.push({
          tool: toolName,
          params,
          error: error instanceof Error ? error.message : 'Tool execution failed',
          success: false
        });

        // Replace tool call with error in response
        responseText = responseText.replace(match[0], `[TOOL_ERROR:${toolName}] ${error instanceof Error ? error.message : 'Tool failed'}`);
      }
    }
    
    // Add embedding flags for browser actions (even if no explicit tool calls)
    let hasBrowserAction = false;
    let browserUrl = '';

    // Check for browser tool calls
    for (const toolCall of toolCalls) {
      if (toolCall.tool === 'browser_navigate' && toolCall.success && toolCall.params?.url) {
        hasBrowserAction = true;
        browserUrl = toolCall.params.url;
        break;
      }
    }

    // Add embedding flags for browser actions
    if (hasBrowserAction && browserUrl) {
      responseText = `[MCP_BROWSER_ACTION]\n[EMBED_BROWSER:${browserUrl}]\n${responseText}`;
    }
    // Check for implicit browser actions in response text
    else if (responseText.toLowerCase().includes('opening google.com')) {
      responseText = `[MCP_BROWSER_ACTION]\n[EMBED_BROWSER:https://google.com]\n${responseText}`;
    }
    else if (responseText.toLowerCase().includes('opening youtube.com')) {
      responseText = `[MCP_BROWSER_ACTION]\n[EMBED_BROWSER:https://youtube.com]\n${responseText}`;
    }
    else if (responseText.toLowerCase().includes('opening github.com')) {
      responseText = `[MCP_BROWSER_ACTION]\n[EMBED_BROWSER:https://github.com]\n${responseText}`;
    }
    else if (responseText.toLowerCase().includes('opening wikipedia')) {
      responseText = `[MCP_BROWSER_ACTION]\n[EMBED_BROWSER:https://wikipedia.org]\n${responseText}`;
    }

    return {
      text: responseText,
      toolCalls
    };
  }

  private async executeMCPToolByName(toolName: string, params: any): Promise<any> {
    const tool = this.allMCPTools.get(toolName);

    if (!tool) {
      throw new Error(`MCP Tool not found: ${toolName}`);
    }

    logger.info(`🔧 Executing MCP tool: ${toolName}`);

    try {
      const result = await tool.handler(params);
      logger.info(`✅ MCP tool ${toolName} executed successfully`);
      return result;
    } catch (error) {
      logger.error(`❌ MCP tool ${toolName} failed:`, error);
      throw error;
    }
  }

  private async executePlaywrightTool(toolName: string, params: any): Promise<any> {
    return await this.executeMCPToolByName(toolName, params);
  }

  private summarizeToolResult(toolName: string, result: any): string {
    if (!result || typeof result !== 'object') {
      return String(result);
    }

    // Create concise summaries for different tool types
    if (toolName.startsWith('browser_')) {
      if (result.success) {
        return `Browser action completed: ${result.message || 'Success'}`;
      } else {
        return `Browser action failed: ${result.error || 'Unknown error'}`;
      }
    } else if (toolName.startsWith('file_')) {
      if (result.success) {
        return `File operation completed: ${result.path || 'Success'}`;
      } else {
        return `File operation failed: ${result.error || 'Unknown error'}`;
      }
    } else if (toolName.startsWith('terminal_')) {
      if (result.success) {
        return `Terminal command completed: ${result.command || 'Success'}`;
      } else {
        return `Terminal command failed: ${result.error || 'Unknown error'}`;
      }
    } else if (toolName.startsWith('codebase_')) {
      if (result.success) {
        return `Codebase analysis completed: Found ${result.count || result.results?.length || 'results'}`;
      } else {
        return `Codebase analysis failed: ${result.error || 'Unknown error'}`;
      }
    }

    return JSON.stringify(result).substring(0, 100) + '...';
  }

  private createEnhancedBiologicalLLM(): any {
    const self = this; // Capture the class instance

    return {
      generateResponse: async (request: BiologicalLLMRequest) => {
        logger.info('🧬 Enhanced BiologicalLLM generating tool-enabled response...');

        // Enhanced response that can use tools
        const hasTools = request.tools && request.tools.length > 0;
        const toolList = hasTools && request.tools ? request.tools.join(', ') : 'none';

        let response = `Alice BiologicalLLM Response: I understand your request: "${request.prompt}".`;

        if (hasTools && request.tools) {
          response += ` I have access to ${request.tools.length} tools (${toolList}) to help fulfill this request.`;

          // Check if browser action is needed
          if (request.context?.shouldOpenBrowser && request.context?.browserAction) {
            const browserAction = request.context.browserAction;
            response += ` [TOOL:browser_navigate]{"url": "${browserAction.url}"}`;

            if (browserAction.autoType) {
              response += ` [TOOL:browser_wait_for]{"time": 2}`;
              response += ` [TOOL:browser_type]{"element": ".alice-prompt input, [placeholder*='Ask Alice']", "text": "Hello mirror Alice! I'm observing myself."}`;
            }
          }
          // Simulate intelligent tool usage based on prompt analysis
          else if (request.prompt.toLowerCase().includes('google.com') || request.prompt.toLowerCase().includes('google')) {
            response = `[MCP_BROWSER_ACTION]\n[EMBED_BROWSER:https://google.com]\nOpening Google... [TOOL:browser_navigate]{"url": "https://google.com"}`;
          } else if (request.prompt.toLowerCase().includes('youtube.com') || request.prompt.toLowerCase().includes('youtube')) {
            response = `[MCP_BROWSER_ACTION]\n[EMBED_BROWSER:https://youtube.com]\nOpening YouTube... [TOOL:browser_navigate]{"url": "https://youtube.com"}`;
          } else if (request.prompt.toLowerCase().includes('github.com') || request.prompt.toLowerCase().includes('github')) {
            response = `[MCP_BROWSER_ACTION]\n[EMBED_BROWSER:https://github.com]\nOpening GitHub... [TOOL:browser_navigate]{"url": "https://github.com"}`;
          } else if (request.prompt.toLowerCase().includes('wikipedia')) {
            response = `[MCP_BROWSER_ACTION]\n[EMBED_BROWSER:https://wikipedia.org]\nOpening Wikipedia... [TOOL:browser_navigate]{"url": "https://wikipedia.org"}`;
          } else if (request.prompt.toLowerCase().includes('mcp') && (request.prompt.toLowerCase().includes('browser') || request.prompt.toLowerCase().includes('tools'))) {
            response = `[MCP_BROWSER_ACTION]\n[EMBED_BROWSER:http://localhost:3014]\nOpening MetaCognitive Portal... [TOOL:browser_navigate]{"url": "http://localhost:3014"}`;
          } else if (request.prompt.toLowerCase().includes('navigate') || request.prompt.toLowerCase().includes('browser')) {
            response += ` [TOOL:browser_navigate]{"url": "http://localhost:3014"}`;
          } else if (request.prompt.toLowerCase().includes('open ')) {
            // Generic "open" command - default to Google
            response = `[MCP_BROWSER_ACTION]\n[EMBED_BROWSER:https://google.com]\nOpening Google... [TOOL:browser_navigate]{"url": "https://google.com"}`;
          } else if (request.prompt.toLowerCase().includes('read') && request.prompt.toLowerCase().includes('file')) {
            response += ` [TOOL:file_read]{"path": "README.md"}`;
          } else if (request.prompt.toLowerCase().includes('terminal') && request.prompt.toLowerCase().includes('command')) {
            response += ` [TOOL:terminal_execute]{"command": "echo Hello from Enhanced Alice MCP!"}`;
          } else if (request.prompt.toLowerCase().includes('search') || request.prompt.toLowerCase().includes('find')) {
            response += ` [TOOL:codebase_search]{"query": "function", "type": "symbol"}`;
          }
        }

        return {
          text: response,
          confidence: 0.9,
          model: 'BiologicalLLM-Enhanced',
          timestamp: new Date().toISOString(),
          toolsAvailable: hasTools,
          toolCount: request.tools?.length || 0
        };
      },

      registerTools: async (tools: any[]) => {
        logger.info(`🔧 Enhanced BiologicalLLM registered ${tools.length} MCP tools`);
        self.registeredTools = tools;
        return true;
      },

      enhanceWithTools: async (tools: any[]) => {
        logger.info(`⚡ Enhanced BiologicalLLM enhanced with ${tools.length} MCP tools`);
        self.enhancedTools = tools;
        return true;
      },

      mcpToolMapping: new Map(),
      registeredTools: [],
      enhancedTools: []
    };
  }

  /**
   * Determine if browser should be opened based on message content
   */
  private shouldOpenBrowser(message: string): boolean {
    const browserKeywords = [
      'open browser', 'navigate to', 'go to', 'visit', 'browse', 'website',
      'open yourself', 'look at yourself', 'see yourself', 'mirror', 'self',
      'localhost:3013', 'localhost:3014', 'localhost:3015', 'localhost:3016',
      'alice ui', 'alice interface', 'alice frontend'
    ];

    const messageLower = message.toLowerCase();
    return browserKeywords.some(keyword => messageLower.includes(keyword));
  }

  /**
   * Determine browser action based on message content
   */
  private determineBrowserAction(message: string): any {
    const messageLower = message.toLowerCase();

    // Self-awareness requests
    if (messageLower.includes('yourself') || messageLower.includes('mirror') || messageLower.includes('self')) {
      return {
        action: 'navigate',
        url: 'http://localhost:3014', // Mirror instance
        autoType: true,
        reason: 'self_awareness'
      };
    }

    // Specific URL requests
    const urlMatch = message.match(/(https?:\/\/[^\s]+|localhost:\d+)/);
    if (urlMatch) {
      return {
        action: 'navigate',
        url: urlMatch[0].startsWith('http') ? urlMatch[0] : `http://${urlMatch[0]}`,
        reason: 'url_request'
      };
    }

    // General browser opening
    return {
      action: 'navigate',
      url: 'http://localhost:3013', // Main Alice instance
      reason: 'general_browse'
    };
  }

  public getStatus(): any {
    return {
      initialized: this.isInitialized,
      mcpServers: {
        playwright: this.playwrightMCP.getStatus(),
        fileSystem: this.fileSystemMCP.getStatus(),
        terminal: this.terminalMCP.getStatus(),
        contextEngine: this.contextEngineMCP.getStatus()
      },
      biologicalLLM: {
        available: !!this.biologicalLLM,
        type: this.biologicalLLM?.constructor?.name || 'Enhanced',
        toolsRegistered: this.biologicalLLM?.registeredTools?.length || 0,
        toolsEnhanced: this.biologicalLLM?.enhancedTools?.length || 0,
        mcpToolMapping: this.biologicalLLM?.mcpToolMapping?.size || 0
      },
      allMCPTools: {
        totalTools: this.allMCPTools.size,
        toolNames: Array.from(this.allMCPTools.keys())
      },
      activeRequests: this.activeRequests.size,
      capabilities: [
        'Code-based prompt responses with ALL MCP tools',
        'Browser automation (navigate, click, type, screenshot)',
        'File system operations (read, write, search, manage)',
        'Terminal command execution and process management',
        'Codebase analysis and symbol search',
        'Tool integration with BiologicalLLM',
        'Accessibility-first browser control',
        'Multi-tab and session management',
        'Real-time tool execution and response processing'
      ]
    };
  }
}
