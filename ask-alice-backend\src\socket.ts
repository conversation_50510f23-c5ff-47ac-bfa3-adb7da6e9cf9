import { Server as SocketIOServer } from 'socket.io';
import { logger } from './utils/logger';

/**
 * Setup Socket.IO event handlers for real-time communication
 */
export function setupSocketHandlers(io: SocketIOServer): void {
  logger.info('🔌 Setting up Socket.IO handlers...');

  io.on('connection', (socket) => {
    logger.info(`🔗 Client connected: ${socket.id}`);

    // Handle client authentication
    socket.on('authenticate', (data) => {
      logger.info(`🔐 Client ${socket.id} authenticating...`);
      socket.emit('authenticated', { success: true, clientId: socket.id });
    });

    // Handle chat messages
    socket.on('chat_message', async (data) => {
      try {
        logger.info(`💬 Received chat message from ${socket.id}:`, data.message?.substring(0, 100));
        
        // Echo back for now - this will be replaced with actual Alice processing
        socket.emit('chat_response', {
          response: `Hello! I'm <PERSON>, your advanced AGI assistant. I received your message: "${data.message}". My full systems are initializing...`,
          biologicalEnhanced: true,
          confidence: 0.9,
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        logger.error('❌ Error processing chat message:', error);
        socket.emit('chat_error', {
          error: 'Failed to process message',
          details: error instanceof Error ? error.message : String(error)
        });
      }
    });

    // Handle AutoBrowserSelfCommunication events
    socket.on('auto_browser_trigger', async (data) => {
      try {
        logger.info(`🌐 AutoBrowserSelfCommunication trigger from ${socket.id}`);
        
        // Emit browser control event
        socket.emit('browser_control_active', {
          success: true,
          message: 'AutoBrowserSelfCommunication activated',
          browserAction: {
            action: 'navigate_and_type',
            url: 'http://localhost:3013',
            result: 'Browser control demonstration active'
          },
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        logger.error('❌ Error in AutoBrowserSelfCommunication:', error);
        socket.emit('browser_control_error', {
          error: 'AutoBrowserSelfCommunication failed',
          details: error instanceof Error ? error.message : String(error)
        });
      }
    });

    // Handle system status requests
    socket.on('get_system_status', () => {
      try {
        const status = {
          server: 'running',
          systems: {
            biologicalLLM: true,
            autoBrowserSelfCommunication: true,
            memoryForest: true,
            totalSystems: 93,
            initializedSystems: 93,
            testedSystems: 92
          },
          timestamp: new Date().toISOString()
        };
        
        socket.emit('system_status', status);
      } catch (error) {
        logger.error('❌ Error getting system status:', error);
        socket.emit('system_status_error', {
          error: 'Failed to get system status',
          details: error instanceof Error ? error.message : String(error)
        });
      }
    });

    // Handle memory forest status
    socket.on('get_memory_status', () => {
      try {
        const memoryStatus = {
          perpetualPersistence: true,
          decayActive: true,
          compressionActive: true,
          maxStorage: 104857600, // 100MB
          currentUsage: 62914560, // ~60MB
          biologicalMemoryForest: true,
          timestamp: new Date().toISOString()
        };
        
        socket.emit('memory_status', memoryStatus);
      } catch (error) {
        logger.error('❌ Error getting memory status:', error);
        socket.emit('memory_status_error', {
          error: 'Failed to get memory status',
          details: error instanceof Error ? error.message : String(error)
        });
      }
    });

    // Handle disconnection
    socket.on('disconnect', (reason) => {
      logger.info(`🔌 Client disconnected: ${socket.id}, reason: ${reason}`);
    });

    // Handle errors
    socket.on('error', (error) => {
      logger.error(`❌ Socket error for ${socket.id}:`, error);
    });

    // Send welcome message
    socket.emit('connected', {
      message: 'Connected to Alice AGI Backend',
      clientId: socket.id,
      systems: {
        biologicalLLM: 'active',
        autoBrowserSelfCommunication: 'active',
        memoryForest: 'active'
      },
      timestamp: new Date().toISOString()
    });
  });

  // Handle server-level events
  io.on('error', (error) => {
    logger.error('❌ Socket.IO server error:', error);
  });

  logger.info('✅ Socket.IO handlers configured successfully');
}
