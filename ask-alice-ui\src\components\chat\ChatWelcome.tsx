import React from 'react';
import { motion } from 'framer-motion';
import {
  SparklesIcon,
  CodeBracketIcon,
  GlobeAltIcon,
  BeakerIcon,
  LightBulbIcon,
  HeartIcon,
} from '@heroicons/react/24/outline';

interface ChatWelcomeProps {
  onSendMessage: (message: string) => void;
}

export const ChatWelcome: React.FC<ChatWelcomeProps> = ({ onSendMessage }) => {
  // Example prompts
  const examplePrompts = [
    {
      title: 'Build me a mindfulness tracker that evolves over time based on my goals.',
      icon: <CodeBracketIcon className="w-5 h-5" />,
    },
    {
      title: 'Design a civilization where AI and humans co-rule with empathy.',
      icon: <GlobeAltIcon className="w-5 h-5" />,
    },
    {
      title: 'Turn this story into an interactive web experience.',
      icon: <LightBulbIcon className="w-5 h-5" />,
    },
    {
      title: 'Dream a new agent species that can survive chaotic systems.',
      icon: <BeakerIcon className="w-5 h-5" />,
    },
    {
      title: 'Plan me a trip that heals my cognitive load and matches my energy.',
      icon: <HeartIcon className="w-5 h-5" />,
    },
    {
      title: 'What should I do next if I feel lost?',
      icon: <SparklesIcon className="w-5 h-5" />,
    },
  ];


  
  return (
    <div className="max-w-3xl mx-auto py-8 md:py-16">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="text-center"
      >
        <div className="w-20 h-20 mx-auto mb-6 rounded-full bg-gradient-to-r from-alice-blue via-alice-purple to-alice-indigo flex items-center justify-center">
          <span className="text-3xl font-bold text-white">A</span>
        </div>
        
        <h1 className="text-3xl md:text-4xl font-bold mb-4 gradient-text">
          Ask Alice
        </h1>
        
        <p className="text-light-text-secondary dark:text-dark-text-secondary mb-8 max-w-lg mx-auto">
          Alice is a decentralized, self-evolving LifeOS and autonomous agent civilization. 
          Ask anything and receive a complete creation, solution, or guidance.
        </p>
      </motion.div>
      
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-8"
      >
        {examplePrompts.map((prompt, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.3 + index * 0.1 }}
          >
            <button
              type="button"
              onClick={() => onSendMessage(prompt.title)}
              className="w-full text-left p-4 rounded-lg border border-light-border dark:border-dark-border hover:bg-light-background-secondary dark:hover:bg-dark-background-secondary transition-colors group"
            >
              <div className="flex items-start gap-3">
                <div className="p-2 rounded-md bg-light-background-tertiary dark:bg-dark-background-tertiary text-light-text-secondary dark:text-dark-text-secondary group-hover:bg-alice-blue/10 group-hover:text-alice-blue dark:group-hover:bg-alice-indigo/10 dark:group-hover:text-alice-indigo transition-colors">
                  {prompt.icon}
                </div>
                <span className="text-light-text-primary dark:text-dark-text-primary">
                  {prompt.title}
                </span>
              </div>
            </button>
          </motion.div>
        ))}
      </motion.div>


      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.8 }}
        className="mt-8 text-center"
      >
        <p className="text-sm text-light-text-tertiary dark:text-dark-text-tertiary">
          Alice may produce inaccurate information about people, places, or facts.
        </p>
      </motion.div>
    </div>
  );
};
