import * as Sentry from '@sentry/node';

export const initializeSentry = () => {
  Sentry.init({
    dsn: process.env.SENTRY_DSN || 'https://<EMAIL>/4509520426500096',
    environment: process.env.NODE_ENV || 'development',

    // Performance Monitoring
    tracesSampleRate: 1.0,

    integrations: [
      // Basic integrations for newer Sentry version
      Sentry.httpIntegration(),
      Sentry.expressIntegration(),
    ],
    
    // Release tracking
    release: process.env.npm_package_version,
    
    // Error filtering
    beforeSend(event, hint) {
      // Filter out common non-critical errors
      if (event.exception) {
        const error = hint.originalException;
        if (error instanceof Error) {
          // Skip CORS errors
          if (error.message.includes('CORS')) {
            return null;
          }
          // Skip connection errors that are expected
          if (error.message.includes('ECONNREFUSED') || error.message.includes('ENOTFOUND')) {
            return null;
          }
        }
      }
      return event;
    },
    
    // Add context
    initialScope: {
      tags: {
        component: 'alice-agi-backend',
        system: 'alice-autonomous-intelligence'
      },
      user: {
        id: 'alice-system',
        username: 'alice'
      }
    }
  });
  
  console.log('🔍 Sentry initialized for Alice AGI Backend');
};

export const captureAliceError = (error: Error, context?: Record<string, any>) => {
  Sentry.withScope((scope) => {
    if (context) {
      scope.setContext('alice_context', context);
    }
    scope.setTag('alice_system', 'true');
    Sentry.captureException(error);
  });
};

export const captureAliceMessage = (message: string, level: 'info' | 'warning' | 'error' = 'info', context?: Record<string, any>) => {
  Sentry.withScope((scope) => {
    if (context) {
      scope.setContext('alice_context', context);
    }
    scope.setTag('alice_system', 'true');
    scope.setLevel(level);
    Sentry.captureMessage(message);
  });
};

export { Sentry };
