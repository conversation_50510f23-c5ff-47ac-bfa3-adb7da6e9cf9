{"name": "ask-alice-backend", "version": "1.0.0", "description": "Backend for the Ask Alice interface", "main": "dist/index.js", "type": "commonjs", "scripts": {"start": "node dist/index.js", "dev": "nodemon --exec ts-node src/index.ts", "build": "tsc", "test": "jest", "lint": "eslint src --ext .ts", "format": "prettier --write \"src/**/*.ts\""}, "dependencies": {"@sentry/node": "^9.30.0", "@types/node-fetch": "^2.6.12", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.1.0", "katex": "^0.16.22", "mermaid": "^11.6.0", "morgan": "^1.10.0", "node-fetch": "^3.3.2", "playwright": "^1.52.0", "rehype-highlight": "^7.0.2", "rehype-katex": "^7.0.1", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "socket.io": "^4.7.2", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.11", "@types/morgan": "^1.9.9", "@types/node": "^20.10.5", "@types/uuid": "^9.0.7", "@types/ws": "^8.18.1", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1", "jest": "^29.7.0", "nodemon": "^3.0.2", "prettier": "^3.1.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}