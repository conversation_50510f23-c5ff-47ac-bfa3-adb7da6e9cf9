#!/usr/bin/env node

/**
 * Test script to verify BiologicalLLM and AutoBrowserSelfCommunication fixes
 * This runs independently of the main server to avoid memory issues
 */

const path = require('path');

// Set up environment
process.env.NODE_ENV = 'development';
process.env.NODE_OPTIONS = '--max-old-space-size=4096';

async function testBiologicalLLM() {
  console.log('🧬 Testing BiologicalLLM...');

  try {
    // Import the module
    const { initializeBiologicalLLM, testBiologicalLLM } = require('./dist/ask-alice-backend/src/services/alice-agi/initialize-biological-llm');
    
    // Initialize
    console.log('🔄 Initializing BiologicalLLM...');
    const initSuccess = await initializeBiologicalLLM();
    
    if (initSuccess) {
      console.log('✅ BiologicalLLM initialization: SUCCESS');
      
      // Test
      console.log('🧪 Testing BiologicalLLM functionality...');
      const testSuccess = await testBiologicalLLM();
      
      if (testSuccess) {
        console.log('✅ BiologicalLLM test: SUCCESS');
        return true;
      } else {
        console.log('❌ BiologicalLLM test: FAILED');
        return false;
      }
    } else {
      console.log('❌ BiologicalLLM initialization: FAILED');
      return false;
    }
  } catch (error) {
    console.error('❌ BiologicalLLM error:', error.message);
    return false;
  }
}

async function testAutoBrowserSelfCommunication() {
  console.log('🌐 Testing AutoBrowserSelfCommunication...');

  try {
    // Import the module
    const { initializeAutoBrowserSelfCommunication, testAutoBrowserSelfCommunication } = require('./dist/ask-alice-backend/src/services/alice-agi/systems/auto-browser-self-communication');
    
    // Create mock blackboard and memory
    const mockBlackboard = {
      publish: async (topic, data) => {
        console.log(`📢 Blackboard publish: ${topic}`, data);
      },
      subscribe: (topic, callback) => {
        console.log(`📡 Blackboard subscribe: ${topic}`);
      }
    };
    
    const mockMemory = {
      storeMemory: async (memory) => {
        console.log('💾 Memory store:', memory.type);
      },
      retrieveMemories: async (query) => {
        console.log('🔍 Memory retrieve:', query);
        return [];
      }
    };
    
    // Initialize
    console.log('🔄 Initializing AutoBrowserSelfCommunication...');
    const initSuccess = await initializeAutoBrowserSelfCommunication(mockBlackboard, mockMemory);
    
    if (initSuccess) {
      console.log('✅ AutoBrowserSelfCommunication initialization: SUCCESS');
      
      // Test
      console.log('🧪 Testing AutoBrowserSelfCommunication functionality...');
      const testSuccess = await testAutoBrowserSelfCommunication();
      
      if (testSuccess) {
        console.log('✅ AutoBrowserSelfCommunication test: SUCCESS');
        return true;
      } else {
        console.log('❌ AutoBrowserSelfCommunication test: FAILED');
        return false;
      }
    } else {
      console.log('❌ AutoBrowserSelfCommunication initialization: FAILED');
      return false;
    }
  } catch (error) {
    console.error('❌ AutoBrowserSelfCommunication error:', error.message);
    return false;
  }
}

async function main() {
  console.log('🚀 Testing specific Alice AGI systems...');
  console.log('═══════════════════════════════════════════');
  
  let successCount = 0;
  let totalTests = 2;
  
  // Test BiologicalLLM
  const biologicalLLMSuccess = await testBiologicalLLM();
  if (biologicalLLMSuccess) successCount++;
  
  console.log('');
  
  // Test AutoBrowserSelfCommunication
  const autoBrowserSuccess = await testAutoBrowserSelfCommunication();
  if (autoBrowserSuccess) successCount++;
  
  console.log('');
  console.log('═══════════════════════════════════════════');
  console.log(`🏁 Test Results: ${successCount}/${totalTests} systems passed`);
  
  if (successCount === totalTests) {
    console.log('🎉 All tests PASSED! The fixes are working correctly.');
    process.exit(0);
  } else {
    console.log('❌ Some tests FAILED. Check the output above for details.');
    process.exit(1);
  }
}

// Handle uncaught errors
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

// Run the tests
main().catch(error => {
  console.error('❌ Test script failed:', error);
  process.exit(1);
});
