import * as Sentry from '@sentry/react';

/**
 * Comprehensive frontend monitoring service for Alice AGI
 * Integrates with Sentry for error tracking, performance monitoring, and user analytics
 */

export interface PerformanceMetrics {
  loadTime: number;
  firstContentfulPaint: number;
  largestContentfulPaint: number;
  firstInputDelay: number;
  cumulativeLayoutShift: number;
  timeToInteractive: number;
}

export interface UserInteraction {
  type: 'click' | 'scroll' | 'input' | 'navigation' | 'chat_message' | 'system_action';
  element?: string;
  timestamp: number;
  duration?: number;
  metadata?: Record<string, any>;
}

export interface SystemHealth {
  memoryUsage: number;
  cpuUsage?: number;
  networkLatency: number;
  errorRate: number;
  activeConnections: number;
}

class MonitoringService {
  private performanceObserver: PerformanceObserver | null = null;
  private userInteractions: UserInteraction[] = [];
  private systemMetrics: SystemHealth | null = null;
  private isInitialized = false;

  /**
   * Initialize the monitoring service
   */
  public initialize(): void {
    if (this.isInitialized) return;

    this.setupPerformanceMonitoring();
    this.setupUserInteractionTracking();
    this.setupSystemHealthMonitoring();
    this.setupErrorTracking();
    
    this.isInitialized = true;
    console.log('🔍 Alice AGI Frontend Monitoring initialized');
  }

  /**
   * Set up performance monitoring using Performance Observer API
   */
  private setupPerformanceMonitoring(): void {
    if (!window.PerformanceObserver) return;

    try {
      // Monitor Core Web Vitals
      this.performanceObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.handlePerformanceEntry(entry);
        }
      });

      // Observe different types of performance entries
      this.performanceObserver.observe({ entryTypes: ['measure', 'navigation', 'paint', 'largest-contentful-paint'] });

      // Monitor Long Tasks (blocking the main thread)
      if ('PerformanceObserver' in window) {
        const longTaskObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            this.trackLongTask(entry);
          }
        });
        
        try {
          longTaskObserver.observe({ entryTypes: ['longtask'] });
        } catch (e) {
          // Long task API not supported
        }
      }
    } catch (error) {
      console.warn('Performance monitoring setup failed:', error);
    }
  }

  /**
   * Handle performance entries and send to Sentry
   */
  private handlePerformanceEntry(entry: PerformanceEntry): void {
    const metrics: Partial<PerformanceMetrics> = {};

    switch (entry.entryType) {
      case 'navigation':
        const navEntry = entry as PerformanceNavigationTiming;
        metrics.loadTime = navEntry.loadEventEnd - navEntry.loadEventStart;
        metrics.timeToInteractive = navEntry.domInteractive - navEntry.navigationStart;
        break;

      case 'paint':
        if (entry.name === 'first-contentful-paint') {
          metrics.firstContentfulPaint = entry.startTime;
        }
        break;

      case 'largest-contentful-paint':
        metrics.largestContentfulPaint = entry.startTime;
        break;
    }

    // Send performance metrics to Sentry
    if (Object.keys(metrics).length > 0) {
      Sentry.addBreadcrumb({
        message: 'Performance Metric',
        category: 'performance',
        level: 'info',
        data: metrics,
      });

      // Set performance context
      Sentry.setContext('performance', metrics);
    }
  }

  /**
   * Track long tasks that block the main thread
   */
  private trackLongTask(entry: PerformanceEntry): void {
    const duration = entry.duration;
    
    if (duration > 50) { // Tasks longer than 50ms are considered problematic
      Sentry.addBreadcrumb({
        message: `Long Task: ${duration.toFixed(2)}ms`,
        category: 'performance',
        level: 'warning',
        data: {
          duration,
          startTime: entry.startTime,
          name: entry.name,
        },
      });

      // Capture as performance issue if very long
      if (duration > 200) {
        Sentry.captureMessage(`Long task detected: ${duration.toFixed(2)}ms`, 'warning');
      }
    }
  }

  /**
   * Set up user interaction tracking
   */
  private setupUserInteractionTracking(): void {
    // Track clicks
    document.addEventListener('click', (event) => {
      this.trackUserInteraction({
        type: 'click',
        element: this.getElementSelector(event.target as Element),
        timestamp: Date.now(),
      });
    });

    // Track scroll events (throttled)
    let scrollTimeout: NodeJS.Timeout;
    document.addEventListener('scroll', () => {
      clearTimeout(scrollTimeout);
      scrollTimeout = setTimeout(() => {
        this.trackUserInteraction({
          type: 'scroll',
          timestamp: Date.now(),
          metadata: {
            scrollY: window.scrollY,
            scrollX: window.scrollX,
          },
        });
      }, 100);
    });

    // Track navigation
    window.addEventListener('popstate', () => {
      this.trackUserInteraction({
        type: 'navigation',
        timestamp: Date.now(),
        metadata: {
          url: window.location.href,
          referrer: document.referrer,
        },
      });
    });
  }

  /**
   * Track user interactions
   */
  public trackUserInteraction(interaction: UserInteraction): void {
    this.userInteractions.push(interaction);

    // Keep only last 100 interactions
    if (this.userInteractions.length > 100) {
      this.userInteractions = this.userInteractions.slice(-100);
    }

    // Add to Sentry breadcrumbs
    Sentry.addBreadcrumb({
      message: `User ${interaction.type}`,
      category: 'user',
      level: 'info',
      data: {
        element: interaction.element,
        metadata: interaction.metadata,
      },
    });
  }

  /**
   * Track Alice-specific events
   */
  public trackAliceEvent(eventType: string, data: Record<string, any>): void {
    Sentry.addBreadcrumb({
      message: `Alice Event: ${eventType}`,
      category: 'alice',
      level: 'info',
      data,
    });

    // Track as custom metric
    Sentry.setTag('alice_event', eventType);
    Sentry.setContext('alice_event_data', data);
  }

  /**
   * Set up system health monitoring
   */
  private setupSystemHealthMonitoring(): void {
    // Monitor memory usage
    setInterval(() => {
      this.updateSystemHealth();
    }, 30000); // Every 30 seconds

    // Monitor network connectivity
    window.addEventListener('online', () => {
      this.trackAliceEvent('network_online', { timestamp: Date.now() });
    });

    window.addEventListener('offline', () => {
      this.trackAliceEvent('network_offline', { timestamp: Date.now() });
    });
  }

  /**
   * Update system health metrics
   */
  private updateSystemHealth(): void {
    const metrics: Partial<SystemHealth> = {};

    // Memory usage
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      metrics.memoryUsage = memory.usedJSHeapSize / memory.jsHeapSizeLimit;
    }

    // Network latency (approximate)
    const startTime = performance.now();
    fetch('/api/health', { method: 'HEAD' })
      .then(() => {
        metrics.networkLatency = performance.now() - startTime;
        this.systemMetrics = { ...this.systemMetrics, ...metrics } as SystemHealth;
        
        // Update Sentry context
        Sentry.setContext('system_health', this.systemMetrics);
      })
      .catch(() => {
        metrics.networkLatency = -1; // Network error
      });
  }

  /**
   * Set up enhanced error tracking
   */
  private setupErrorTracking(): void {
    // Track unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      Sentry.captureException(event.reason, {
        tags: {
          errorType: 'unhandled_promise_rejection',
        },
        extra: {
          promise: event.promise,
          reason: event.reason,
        },
      });
    });

    // Track console errors
    const originalError = console.error;
    console.error = (...args) => {
      originalError.apply(console, args);
      
      if (args.length > 0 && typeof args[0] === 'string') {
        Sentry.addBreadcrumb({
          message: args[0],
          category: 'console',
          level: 'error',
          data: { arguments: args.slice(1) },
        });
      }
    };
  }

  /**
   * Get CSS selector for an element
   */
  private getElementSelector(element: Element): string {
    if (!element) return 'unknown';

    if (element.id) return `#${element.id}`;
    if (element.className && typeof element.className === 'string') {
      return `.${element.className.split(' ')[0]}`;
    }
    return element.tagName.toLowerCase();
  }

  /**
   * Get current performance metrics
   */
  public getPerformanceMetrics(): PerformanceMetrics | null {
    if (!window.performance) return null;

    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    if (!navigation) return null;

    return {
      loadTime: navigation.loadEventEnd - navigation.loadEventStart,
      firstContentfulPaint: performance.getEntriesByName('first-contentful-paint')[0]?.startTime || 0,
      largestContentfulPaint: performance.getEntriesByName('largest-contentful-paint')[0]?.startTime || 0,
      firstInputDelay: 0, // Would need to be measured separately
      cumulativeLayoutShift: 0, // Would need to be measured separately
      timeToInteractive: navigation.domInteractive - navigation.navigationStart,
    };
  }

  /**
   * Get recent user interactions
   */
  public getUserInteractions(): UserInteraction[] {
    return [...this.userInteractions];
  }

  /**
   * Get current system health
   */
  public getSystemHealth(): SystemHealth | null {
    return this.systemMetrics;
  }

  /**
   * Cleanup monitoring
   */
  public cleanup(): void {
    if (this.performanceObserver) {
      this.performanceObserver.disconnect();
      this.performanceObserver = null;
    }
    this.isInitialized = false;
  }
}

// Export singleton instance
export const monitoringService = new MonitoringService();

// Auto-initialize when imported
if (typeof window !== 'undefined') {
  monitoringService.initialize();
}

export default monitoringService;
