﻿import { Server as SocketIOServer, Socket } from 'socket.io';
import { logger } from '../utils/logger';
import { processMessage } from '../services/alice-agi/process-message-fixed';
import { RealTimeProcessingOrchestrator } from '../services/alice-agi/real-time-processing-orchestrator';

// Initialize the real-time processing orchestrator
const processingOrchestrator = new RealTimeProcessingOrchestrator();

export const setupSocketHandlers = (io: SocketIOServer): void => {
  io.on('connection', (socket: Socket) => {
    logger.info(`Client connected: ${socket.id}`);

    // Handle test connection event
    socket.on('test_connection', (data) => {
      logger.info(`Test connection received from ${socket.id}: ${JSON.stringify(data)}`);
      socket.emit('test_connection_response', { status: 'success', message: 'Connection established' });
    });

    // Handle ping event to keep connection alive
    socket.on('ping', () => {
      logger.debug(`Ping received from ${socket.id}`);
      socket.emit('pong');
    });

    // Handle chat messages
    socket.on('send_message', async (data) => {
      try {
        const { chatId, message, userId } = data;
        logger.info(`Socket: Message received from ${userId} in chat ${chatId}: ${message?.substring(0, 50)}${message?.length > 50 ? '...' : ''}`);

        // Validate input
        if (!chatId || !message || !userId) {
          const error = 'Missing required fields: chatId, message, or userId';
          logger.error(`Socket: ${error}`, { chatId, userId, messageLength: message?.length });
          socket.emit('processing_error', {
            chatId,
            error: 'Validation error',
            details: error
          });
          return; // Don't throw, just return
        }

        // Emit acknowledgment
        logger.info(`Socket: Sending message_received acknowledgment for chat ${chatId}`);
        socket.emit('message_received', { chatId, messageId: Date.now() });

        // Start processing the message
        logger.info(`Socket: Sending processing_started event for chat ${chatId}`);
        socket.emit('processing_started', { chatId });

        // Check for simple test messages and provide immediate response
        const isTestMessage = message.toLowerCase().includes('test') ||
                             message.toLowerCase().includes('hello') ||
                             message.toLowerCase().includes('hi') ||
                             message.toLowerCase().includes('simple');

        if (isTestMessage) {
          logger.info(`Socket: Detected test message, providing immediate response for chat ${chatId}`);

          const testResponse = `Hello! I'm Alice, your advanced AGI assistant. I received your message: "${message}"

🎉 **Socket.IO Connection Working!**
✅ Backend is receiving messages properly
✅ Processing pipeline is active
✅ Response system is functional

I'm currently running with:
- 🧠 Advanced consciousness systems
- 🧬 BiologicalLLM integration
- 🔧 MCP tool capabilities
- 💭 Perpetual memory systems

What would you like to explore together?`;

          // Emit the response immediately
          socket.emit('message_response', {
            chatId,
            response: testResponse,
            timestamp: new Date().toISOString()
          });

          // Emit processing completed
          socket.emit('processing_completed', { chatId });
          socket.emit('processing_complete', { chatId }); // backward compatibility

          logger.info(`Socket: Sent immediate test response for chat ${chatId}`);
          return;
        }

        // Process through Alice AGI's real 7-stage pipeline using the orchestrator
        logger.info(`Socket: Processing message with Real-Time Processing Orchestrator for chat ${chatId}`);
        const result = await processingOrchestrator.processMessage(chatId, message, userId, socket);

        // Handle the response from the orchestrator
        const response = typeof result === 'string' ? result : JSON.stringify(result);

        logger.info(`Socket: Received response from Real-Time Processing Orchestrator for chat ${chatId}: ${response.substring(0, 50)}${response.length > 50 ? '...' : ''}`);

        // Emit the response
        logger.info(`Socket: Emitting message_response for chat ${chatId}`);
        socket.emit('message_response', {
          chatId,
          response,
          timestamp: new Date().toISOString()
        });

        // Emit processing completed
        logger.info(`Socket: Emitting processing_completed for chat ${chatId}`);
        socket.emit('processing_completed', { chatId });

        // Also emit the old event names for backward compatibility
        socket.emit('message', {
          chatId,
          content: response,
          timestamp: new Date().toISOString()
        });
        socket.emit('processing_complete', { chatId });
      } catch (error) {
        logger.error('Socket: Error processing message:', error);
        logger.error(`Socket: Error details: ${error instanceof Error ? error.message : String(error)}`);

        // Include chatId in the error response
        socket.emit('processing_error', {
          chatId: data?.chatId || 'unknown',
          error: 'Failed to process message',
          details: error instanceof Error ? error.message : String(error)
        });
        logger.info(`Socket: Emitted processing_error event for chat ${data?.chatId || 'unknown'}`);
      }
    });

    // Handle stop processing requests
    socket.on('stop_processing', (data) => {
      try {
        const { chatId } = data;
        logger.info(`Socket: Stop processing requested for chat ${chatId}`);

        // Emit processing stopped event
        socket.emit('processing_stopped', { chatId });
        logger.info(`Socket: Emitted processing_stopped event for chat ${chatId}`);
      } catch (error) {
        logger.error('Socket: Error stopping processing:', error);
        socket.emit('processing_error', {
          chatId: data?.chatId || 'unknown',
          error: 'Failed to stop processing',
          details: error instanceof Error ? error.message : String(error)
        });
      }
    });

    // Handle dashboard data subscriptions
    socket.on('subscribe_dashboard', (dashboard) => {
      logger.info(`Client ${socket.id} subscribed to ${dashboard} dashboard`);
      socket.join(`dashboard:${dashboard}`);
    });

    // Handle dashboard data unsubscriptions
    socket.on('unsubscribe_dashboard', (dashboard) => {
      logger.info(`Client ${socket.id} unsubscribed from ${dashboard} dashboard`);
      socket.leave(`dashboard:${dashboard}`);
    });

    // Handle disconnection
    socket.on('disconnect', () => {
      logger.info(`Client disconnected: ${socket.id}`);
    });
  });

  // Set up periodic dashboard updates
  setInterval(() => {
    // Update mutation impact dashboard
    io.to('dashboard:mutation-impact').emit('dashboard_update', {
      dashboard: 'mutation-impact',
      data: generateMockMutationImpactData(),
    });

    // Update cognitive stability dashboard
    io.to('dashboard:cognitive-stability').emit('dashboard_update', {
      dashboard: 'cognitive-stability',
      data: generateMockCognitiveStabilityData(),
    });

    // Update other dashboards similarly
  }, 5000); // Update every 5 seconds
};

// Mock data generators for dashboards
function generateMockMutationImpactData() {
  return {
    timestamp: new Date().toISOString(),
    overallImpact: Math.floor(70 + Math.random() * 20),
    recentMutations: [
      {
        id: `mut-${Date.now()}`,
        type: 'Neural Network',
        target: 'PerceptionEngine',
        impact: Math.floor(60 + Math.random() * 30),
        timestamp: new Date().toISOString(),
      },
      // Add more mock mutations as needed
    ],
  };
}

function generateMockCognitiveStabilityData() {
  return {
    timestamp: new Date().toISOString(),
    overallStability: Math.floor(80 + Math.random() * 15),
    components: [
      {
        id: 'comp-1',
        name: 'Consciousness Model',
        stability: Math.floor(85 + Math.random() * 10),
      },
      {
        id: 'comp-2',
        name: 'Memory Forest',
        stability: Math.floor(80 + Math.random() * 15),
      },
      // Add more components as needed
    ],
  };
}




