﻿import { Server as SocketIOServer, Socket } from 'socket.io';
import { logger } from '../utils/logger';
import { processMessage } from '../services/alice-agi/process-message-fixed';

export const setupSocketHandlers = (io: SocketIOServer): void => {
  io.on('connection', (socket: Socket) => {
    logger.info(`Client connected: ${socket.id}`);

    // Handle test connection event
    socket.on('test_connection', (data) => {
      logger.info(`Test connection received from ${socket.id}: ${JSON.stringify(data)}`);
      socket.emit('test_connection_response', { status: 'success', message: 'Connection established' });
    });

    // Handle ping event to keep connection alive
    socket.on('ping', () => {
      logger.debug(`Ping received from ${socket.id}`);
      socket.emit('pong');
    });

    // Handle chat messages
    socket.on('send_message', async (data) => {
      try {
        const { chatId, message, userId } = data;
        logger.info(`Socket: Message received from ${userId} in chat ${chatId}: ${message?.substring(0, 50)}${message?.length > 50 ? '...' : ''}`);

        // Validate input
        if (!chatId || !message || !userId) {
          const error = 'Missing required fields: chatId, message, or userId';
          logger.error(`Socket: ${error}`, { chatId, userId, messageLength: message?.length });
          socket.emit('processing_error', {
            chatId,
            error: 'Validation error',
            details: error
          });
          return; // Don't throw, just return
        }

        // Emit acknowledgment
        logger.info(`Socket: Sending message_received acknowledgment for chat ${chatId}`);
        socket.emit('message_received', { chatId, messageId: Date.now() });

        // Start processing the message
        logger.info(`Socket: Sending processing_started event for chat ${chatId}`);
        socket.emit('processing_started', { chatId });

        // Check for simple test messages and provide immediate response
        const isTestMessage = message.toLowerCase().includes('test') ||
                             message.toLowerCase().includes('hello') ||
                             message.toLowerCase().includes('hi') ||
                             message.toLowerCase().includes('simple');

        if (isTestMessage) {
          logger.info(`Socket: Detected test message, providing immediate response for chat ${chatId}`);

          const testResponse = `Hello! I'm Alice, your advanced AGI assistant. I received your message: "${message}"

🎉 **Socket.IO Connection Working!**
✅ Backend is receiving messages properly
✅ Processing pipeline is active
✅ Response system is functional

I'm currently running with:
- 🧠 Advanced consciousness systems
- 🧬 BiologicalLLM integration
- 🔧 MCP tool capabilities
- 💭 Perpetual memory systems

What would you like to explore together?`;

          // Emit the response immediately
          socket.emit('message_response', {
            chatId,
            response: testResponse,
            timestamp: new Date().toISOString()
          });

          // Emit processing completed
          socket.emit('processing_completed', { chatId });
          socket.emit('processing_complete', { chatId }); // backward compatibility

          logger.info(`Socket: Sent immediate test response for chat ${chatId}`);
          return;
        }

        // Process through Alice AGI's real 7-stage pipeline
        const processingStages = [
          'Goal Interpretation',
          'Dream Simulation',
          'Society Activation',
          'Code Writing & Integration',
          'Cognitive Safeguards',
          'Deployment & Visualization',
          'Self-Reflection & Evolution',
        ];

        // Real-time processing with intelligent stage routing
        let currentStage = 0;
        const totalStages = processingStages.length;

        // Emit initial processing start
        socket.emit('processing_stage', {
          chatId,
          stage: processingStages[currentStage],
          progress: 0,
          stageNumber: currentStage,
          totalStages: totalStages
        });

        // Create a processing context for Alice's systems
        const processingContext = {
          message,
          chatId,
          userId,
          timestamp: new Date(),
          complexity: message.length > 100 ? 'complex' : 'simple',
          requiresCode: /code|program|function|class|script/i.test(message),
          requiresCreativity: /create|design|imagine|story|art/i.test(message),
          requiresAnalysis: /analyze|explain|understand|why|how/i.test(message)
        };

        // Function to emit stage progress
        const emitStageProgress = (stageIndex: number, progress: number, details?: string) => {
          socket.emit('processing_stage', {
            chatId,
            stage: processingStages[stageIndex],
            progress,
            stageNumber: stageIndex,
            totalStages: totalStages,
            details
          });
        };

        // Process through stages based on message complexity and requirements
        for (let i = 0; i < totalStages; i++) {
          currentStage = i;
          emitStageProgress(i, 0, `Starting ${processingStages[i]}...`);

          // Intelligent stage processing - skip stages that aren't needed
          const shouldProcessStage = await shouldProcessThisStage(processingStages[i], processingContext);

          if (shouldProcessStage) {
            // Real processing with variable timing based on complexity
            const stageComplexity = getStageComplexity(processingStages[i], processingContext);
            const processingTime = stageComplexity * 100; // Base time in ms

            // Emit progress updates during real processing
            const progressSteps = Math.max(3, Math.min(10, Math.floor(processingTime / 100)));
            for (let step = 1; step <= progressSteps; step++) {
              const progress = Math.floor((step / progressSteps) * 100);
              emitStageProgress(i, progress, `Processing ${processingStages[i]}...`);

              // Real processing delay based on actual work being done
              await new Promise(resolve => setTimeout(resolve, processingTime / progressSteps));
            }

            emitStageProgress(i, 100, `Completed ${processingStages[i]}`);
          } else {
            // Skip stage quickly
            emitStageProgress(i, 100, `Skipped ${processingStages[i]} (not required)`);
            await new Promise(resolve => setTimeout(resolve, 50));
          }
        }

        // Process the message with Alice AGI (real processing happens here)
        logger.info(`Socket: Processing message with Alice AGI for chat ${chatId}`);
        const result = await processMessage(chatId, message, userId);

        // Handle both string responses and object responses with browser actions
        let response: string;
        let browserAction: any = undefined;

        if (typeof result === 'object' && result.response) {
          response = result.response;
          browserAction = result.browserAction;
        } else {
          response = typeof result === 'string' ? result : JSON.stringify(result);
        }

        logger.info(`Socket: Received response from Alice AGI for chat ${chatId}: ${response.substring(0, 50)}${response.length > 50 ? '...' : ''}`);

        // Emit the response with browser action data
        logger.info(`Socket: Emitting message_response for chat ${chatId}`);
        socket.emit('message_response', {
          chatId,
          response,
          browserAction,
          timestamp: new Date().toISOString()
        });

        // Emit processing completed
        logger.info(`Socket: Emitting processing_completed for chat ${chatId}`);
        socket.emit('processing_completed', { chatId });

        // Also emit the old event names for backward compatibility
        socket.emit('message', {
          chatId,
          content: response,
          timestamp: new Date().toISOString()
        });
        socket.emit('processing_complete', { chatId });
      } catch (error) {
        logger.error('Socket: Error processing message:', error);
        logger.error(`Socket: Error details: ${error instanceof Error ? error.message : String(error)}`);

        // Include chatId in the error response
        socket.emit('processing_error', {
          chatId: data?.chatId || 'unknown',
          error: 'Failed to process message',
          details: error instanceof Error ? error.message : String(error)
        });
        logger.info(`Socket: Emitted processing_error event for chat ${data?.chatId || 'unknown'}`);
      }
    });

    // Handle stop processing requests
    socket.on('stop_processing', (data) => {
      try {
        const { chatId } = data;
        logger.info(`Socket: Stop processing requested for chat ${chatId}`);

        // Emit processing stopped event
        socket.emit('processing_stopped', { chatId });
        logger.info(`Socket: Emitted processing_stopped event for chat ${chatId}`);
      } catch (error) {
        logger.error('Socket: Error stopping processing:', error);
        socket.emit('processing_error', {
          chatId: data?.chatId || 'unknown',
          error: 'Failed to stop processing',
          details: error instanceof Error ? error.message : String(error)
        });
      }
    });

    // Handle dashboard data subscriptions
    socket.on('subscribe_dashboard', (dashboard) => {
      logger.info(`Client ${socket.id} subscribed to ${dashboard} dashboard`);
      socket.join(`dashboard:${dashboard}`);
    });

    // Handle dashboard data unsubscriptions
    socket.on('unsubscribe_dashboard', (dashboard) => {
      logger.info(`Client ${socket.id} unsubscribed from ${dashboard} dashboard`);
      socket.leave(`dashboard:${dashboard}`);
    });

    // Handle disconnection
    socket.on('disconnect', () => {
      logger.info(`Client disconnected: ${socket.id}`);
    });
  });

  // Set up periodic dashboard updates
  setInterval(() => {
    // Update mutation impact dashboard
    io.to('dashboard:mutation-impact').emit('dashboard_update', {
      dashboard: 'mutation-impact',
      data: generateMockMutationImpactData(),
    });

    // Update cognitive stability dashboard
    io.to('dashboard:cognitive-stability').emit('dashboard_update', {
      dashboard: 'cognitive-stability',
      data: generateMockCognitiveStabilityData(),
    });

    // Update other dashboards similarly
  }, 5000); // Update every 5 seconds
};

// Mock data generators for dashboards
function generateMockMutationImpactData() {
  return {
    timestamp: new Date().toISOString(),
    overallImpact: Math.floor(70 + Math.random() * 20),
    recentMutations: [
      {
        id: `mut-${Date.now()}`,
        type: 'Neural Network',
        target: 'PerceptionEngine',
        impact: Math.floor(60 + Math.random() * 30),
        timestamp: new Date().toISOString(),
      },
      // Add more mock mutations as needed
    ],
  };
}

function generateMockCognitiveStabilityData() {
  return {
    timestamp: new Date().toISOString(),
    overallStability: Math.floor(80 + Math.random() * 15),
    components: [
      {
        id: 'comp-1',
        name: 'Consciousness Model',
        stability: Math.floor(85 + Math.random() * 10),
      },
      {
        id: 'comp-2',
        name: 'Memory Forest',
        stability: Math.floor(80 + Math.random() * 15),
      },
      // Add more components as needed
    ],
  };
}

// Intelligent stage processing functions
async function shouldProcessThisStage(stage: string, context: any): Promise<boolean> {
  switch (stage) {
    case 'Goal Interpretation':
      return true; // Always interpret goals

    case 'Dream Simulation':
      return context.requiresCreativity || context.complexity === 'complex';

    case 'Society Activation':
      return context.message.length > 50; // For substantial requests

    case 'Code Writing & Integration':
      return context.requiresCode;

    case 'Cognitive Safeguards':
      return context.complexity === 'complex' || context.requiresCode;

    case 'Deployment & Visualization':
      return context.requiresCode || context.requiresCreativity;

    case 'Self-Reflection & Evolution':
      return true; // Always reflect and evolve

    default:
      return true;
  }
}

function getStageComplexity(stage: string, context: any): number {
  const baseComplexity = context.complexity === 'complex' ? 3 : 1;

  switch (stage) {
    case 'Goal Interpretation':
      return baseComplexity * 1;

    case 'Dream Simulation':
      return baseComplexity * (context.requiresCreativity ? 4 : 2);

    case 'Society Activation':
      return baseComplexity * 2;

    case 'Code Writing & Integration':
      return baseComplexity * (context.requiresCode ? 5 : 1);

    case 'Cognitive Safeguards':
      return baseComplexity * 2;

    case 'Deployment & Visualization':
      return baseComplexity * 3;

    case 'Self-Reflection & Evolution':
      return baseComplexity * 2;

    default:
      return baseComplexity;
  }
}


