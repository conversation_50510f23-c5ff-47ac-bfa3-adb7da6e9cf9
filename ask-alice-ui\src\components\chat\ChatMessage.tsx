import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { motion } from 'framer-motion';
import { useTheme } from '../../hooks/useTheme';
import { useBrowserDetachment } from '../../contexts/BrowserDetachmentContext';
import { PersistentBrowserWindow } from '../browser/PersistentBrowserWindow';
import { EnhancedMarkdownRenderer } from '../markdown/EnhancedMarkdownRenderer';
import { MermaidRenderer } from '../markdown/MermaidRenderer';
import {
  ChatBubbleLeftIcon,
  UserIcon,
  EllipsisHorizontalIcon,
  ArrowPathIcon,
  GlobeAltIcon,
  CheckIcon,
} from '@heroicons/react/24/outline';

interface ChatMessageProps {
  message: {
    id: string;
    role: 'user' | 'assistant' | 'system';
    content: string;
    timestamp: string;
    status?: 'loading' | 'complete' | 'error';
    processing?: {
      stage: string;
      progress: number;
    };
    browserAction?: {
      action: string;
      url?: string;
      result?: any;
      success?: boolean;
    };
    specializedProcessing?: {
      browserControl?: {
        action: string;
        url?: string;
        result?: any;
        success?: boolean;
      };
    };
  };
  isLastMessage: boolean;
  chatId?: string;
}

export const ChatMessage: React.FC<ChatMessageProps> = ({ message, isLastMessage, chatId }) => {
  const { resolvedTheme } = useTheme();
  const {
    showGlobalBrowser,
    hideGlobalBrowser,
    globalBrowserState,
    setCurrentChatId,
    getOrCreatePersistentBrowser,
    hasPersistentBrowser,
    detachBrowserForChat,
    reattachBrowserForChat,
    setActiveBrowserMessage,
    getActiveBrowserMessage,
  } = useBrowserDetachment();
  const [copied, setCopied] = useState(false);
  const [showNestedBrowser, setShowNestedBrowser] = useState(false);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const messageRef = useRef<HTMLDivElement>(null);
  const browserRef = useRef<HTMLDivElement>(null);
  const browserCreationInProgress = useRef(false);

  // Check if this message has browser control data
  const hasBrowserControl = message.specializedProcessing?.browserControl;
  const browserUrl = hasBrowserControl?.url;

  // Format timestamp
  const formattedTime = new Date(message.timestamp).toLocaleTimeString([], {
    hour: '2-digit',
    minute: '2-digit',
  });

  // Intelligent browser content detection - detect when Alice actually opens browsers
  const hasBrowserContent = useMemo(() => {
    return message.role === 'assistant' && (
      message.browserAction ||
      hasBrowserControl ||
      // REAL MCP tool execution patterns
      message.content.includes('[EMBED_BROWSER:') ||
      (message.content.includes('🔧 Tool browser_navigate:') && message.content.includes('Executed successfully')) ||
      (message.content.includes('🌐 Browser Navigation:') && message.content.includes('Successfully navigated')) ||
      (message.content.includes('[TOOL:browser_') && message.content.includes(']') && message.content.includes('Executed')) ||
      // Alice's current response patterns when she actually opens browsers
      (message.content.toLowerCase().includes('opening google.com') && message.content.length < 50) ||
      (message.content.toLowerCase().includes('opening youtube.com') && message.content.length < 50) ||
      (message.content.toLowerCase().includes('opening github.com') && message.content.length < 50) ||
      // Short responses that indicate actual browser opening (not long explanations)
      (message.content.toLowerCase().includes('opening') && message.content.toLowerCase().includes('.com') && message.content.length < 100)
    );
  }, [message.role, message.browserAction, hasBrowserControl, message.content]);

  // Auto-show browser for new messages with browser content
  useEffect(() => {
    if (hasBrowserContent && chatId && !showNestedBrowser && isLastMessage) {
      // Only auto-show if this is the latest message with browser content
      // and no browser is currently showing for this message
      const currentActiveBrowser = getActiveBrowserMessage(chatId);
      if (!currentActiveBrowser || currentActiveBrowser === message.id) {
        // Create browser and show it
        const createBrowserAndShow = async () => {
          try {
            const content = message.content.toLowerCase();
            let url = 'https://google.com';

            // Extract URL from message content using same logic as button
            const embedMatch = message.content.match(/\[EMBED_BROWSER:([^\]]+)\]/);
            if (embedMatch) {
              url = embedMatch[1];
            } else {
              const toolMatch = message.content.match(/\[TOOL:browser_navigate\]\{"url":\s*"([^"]+)"\}/);
              const mcpNavigationMatch = message.content.match(/🌐 Browser Navigation: Successfully navigated to ([^\s\n]+)/);
              const mcpToolMatch = message.content.match(/🔧 Tool browser_navigate: Executed successfully.*url[:\s]*([^\s\n,}]+)/);

              if (toolMatch) {
                url = toolMatch[1];
              } else if (mcpNavigationMatch) {
                url = mcpNavigationMatch[1];
              } else if (mcpToolMatch) {
                url = mcpToolMatch[1];
              } else if (content.includes('opening google.com')) url = 'https://google.com';
              else if (content.includes('opening youtube.com')) url = 'https://youtube.com';
              else if (content.includes('opening github.com')) url = 'https://github.com';
              else if (content.includes('opening wikipedia')) url = 'https://wikipedia.org';
              else if (content.includes('mirror') && content.includes('alice') &&
                      (content.includes('observe') || content.includes('recursive'))) {
                url = 'http://localhost:3014';
              }
            }

            await getOrCreatePersistentBrowser(chatId, url);
            setActiveBrowserMessage(chatId, message.id);
            setShowNestedBrowser(true);
          } catch (error) {
            console.error('Failed to auto-create browser:', error);
          }
        };

        createBrowserAndShow();
      }
    }
  }, [hasBrowserContent, chatId, isLastMessage, message.id, showNestedBrowser, getActiveBrowserMessage, setActiveBrowserMessage, getOrCreatePersistentBrowser, message.content]);

  // Handle browser detachment - move persistent browser to floating state
  const handleBrowserDetach = useCallback(() => {
    if (chatId && showNestedBrowser) {
      console.log('🚀 Starting magical browser transition...');

      // Start the transition effect with enhanced animation
      setIsTransitioning(true);

      // First, create the floating window immediately for seamless transition
      detachBrowserForChat(chatId);
      setActiveBrowserMessage(chatId, ''); // Clear active browser message

      // Enhanced smooth transition with fade effect
      // This creates the magical "popping out" effect
      setTimeout(() => {
        setShowNestedBrowser(false);
        setIsTransitioning(false);
        console.log('✨ Browser magically transitioned to floating mode!');
      }, 400); // Increased to 400ms for smoother transition
    }
  }, [chatId, showNestedBrowser, detachBrowserForChat, setActiveBrowserMessage]);

  // Scroll-based auto-detach functionality
  useEffect(() => {
    if (!showNestedBrowser || !browserRef.current || !chatId) return;

    const browserElement = browserRef.current;
    let hasDetached = false; // Prevent multiple detach calls

    // Create intersection observer to detect when browser scrolls out of view
    const observer = new IntersectionObserver(
      (entries) => {
        const entry = entries[0];

        // If browser is less than 20% visible and scrolling up, auto-detach to floating
        // Only trigger once per browser instance
        if (!hasDetached && entry.intersectionRatio < 0.2 && entry.boundingClientRect.top < 0) {
          console.log('🔄 Auto-detaching browser due to scroll');
          hasDetached = true; // Mark as detached to prevent duplicates
          handleBrowserDetach();
        }
      },
      {
        threshold: [0, 0.2, 0.5, 1.0], // Multiple thresholds for smooth detection
        rootMargin: '0px 0px -50px 0px' // Trigger slightly before completely out of view
      }
    );

    observer.observe(browserElement);

    return () => {
      observer.disconnect();
    };
  }, [showNestedBrowser, chatId, handleBrowserDetach]); // Added handleBrowserDetach to dependencies

  // Removed setCurrentChatId useEffect to prevent infinite re-renders
  // The chat ID will be set when browser actions are triggered manually

  // DISABLED: Auto-browser creation to prevent infinite re-renders
  // Browser will only be shown when user manually clicks the Browser button
  // This prevents the infinite loop issue that was causing performance problems

  // Handle browser close - reattach to nested state
  const handleBrowserClose = () => {
    if (chatId) {
      reattachBrowserForChat(chatId);
      setActiveBrowserMessage(chatId, ''); // Clear active browser message
    }
    setShowNestedBrowser(false);
  };

  // Copy message content to clipboard
  const copyToClipboard = () => {
    navigator.clipboard.writeText(message.content);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  // Regenerate response
  const regenerateResponse = () => {
    // In a real implementation, this would regenerate the response
    console.log('Regenerate response');
  };
  
  return (
    <>
    <motion.div
      ref={messageRef}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="py-6 group"
    >
      <div className="max-w-none">
        <div className="flex items-start gap-3">
          {/* Simple Avatar */}
          <div className={`w-7 h-7 rounded-full flex items-center justify-center flex-shrink-0 ${
            message.role === 'assistant'
              ? 'bg-gradient-to-r from-alice-blue to-alice-purple text-white'
              : 'bg-light-background-tertiary dark:bg-dark-background-tertiary'
          }`}>
            {message.role === 'assistant' ? (
              <span className="font-bold text-sm">A</span>
            ) : (
              <UserIcon className="w-4 h-4" />
            )}
          </div>

          {/* Message content */}
          <div className="flex-1 min-w-0">
            {/* Simple header */}
            <div className="flex items-center gap-2 mb-2">
              <span className="font-medium text-light-text-primary dark:text-dark-text-primary text-sm">
                {message.role === 'assistant' ? 'Alice' : 'You'}
              </span>
              <span className="text-xs text-light-text-tertiary dark:text-dark-text-tertiary">
                {new Date(message.timestamp).toLocaleTimeString([], {
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </span>
            </div>

            {message.status === 'loading' ? (
              <div className="flex items-center gap-2 text-light-text-secondary dark:text-dark-text-secondary">
                <div className="animate-pulse">
                  <EllipsisHorizontalIcon className="w-5 h-5" />
                </div>
                <span className="text-sm">
                  {message.processing
                    ? `${message.processing.stage} (${Math.round(message.processing.progress)}%)`
                    : 'Alice is thinking...'}
                </span>
              </div>
            ) : (
              <div className="enhanced-markdown-response">
                {/* Beautiful Enhanced Markdown with Mermaid support */}
                {message.content.includes('```mermaid') ? (
                  <div>
                    {message.content.split(/(```mermaid[\s\S]*?```)/g).map((part, index) => {
                      if (part.startsWith('```mermaid')) {
                        const mermaidCode = part.replace(/```mermaid\n?/, '').replace(/\n?```$/, '');
                        return <MermaidRenderer key={index} chart={mermaidCode} />;
                      } else if (part.trim()) {
                        return <EnhancedMarkdownRenderer key={index} content={part} />;
                      }
                      return null;
                    })}
                  </div>
                ) : (
                  <EnhancedMarkdownRenderer content={message.content} />
                )}
              </div>
            )}
          </div>
        </div>

        {/* Simple message actions */}
        {message.role === 'assistant' && message.status === 'complete' && (
          <div className="flex justify-end mt-3 gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
            <button
              type="button"
              onClick={copyToClipboard}
              className="flex items-center gap-1 px-2 py-1 text-xs rounded text-light-text-tertiary dark:text-dark-text-tertiary hover:bg-light-background-tertiary dark:hover:bg-dark-background-tertiary transition-colors"
              aria-label="Copy message"
            >
              {copied ? (
                <>
                  <CheckIcon className="w-3 h-3" />
                  <span>Copied</span>
                </>
              ) : (
                <>
                  <ChatBubbleLeftIcon className="w-3 h-3" />
                  <span>Copy</span>
                </>
              )}
            </button>

            {hasBrowserContent && (
              <button
                type="button"
                onClick={async () => {
                  if (!chatId) return;

                  if (showNestedBrowser) {
                    // Currently showing nested - detach to floating
                    handleBrowserDetach();
                  } else if (globalBrowserState.isVisible && globalBrowserState.chatId === chatId) {
                    // Currently floating - hide completely
                    hideGlobalBrowser();
                    reattachBrowserForChat(chatId);
                  } else {
                    // Not showing - create/show persistent browser with enhanced MCP URL detection
                    try {
                      const content = message.content.toLowerCase();
                      let url = browserUrl || 'https://google.com';

                      // Priority 1: Extract URL from MCP embedding flag (new method)
                      const embedMatch = message.content.match(/\[EMBED_BROWSER:([^\]]+)\]/);
                      if (embedMatch) {
                        url = embedMatch[1];
                      }
                      // Priority 2: Extract URL from MCP tool usage (BiologicalLLM integration)
                      else {
                        const toolMatch = message.content.match(/\[TOOL:browser_navigate\]\{"url":\s*"([^"]+)"\}/);
                        const mcpNavigationMatch = message.content.match(/🌐 Browser Navigation: Successfully navigated to ([^\s\n]+)/);
                        const mcpToolMatch = message.content.match(/🔧 Tool browser_navigate: Executed successfully.*url[:\s]*([^\s\n,}]+)/);

                        if (toolMatch) {
                          url = toolMatch[1];
                        } else if (mcpNavigationMatch) {
                          url = mcpNavigationMatch[1];
                        } else if (mcpToolMatch) {
                          url = mcpToolMatch[1];
                        }
                        // Priority 3: Alice's current response patterns (fix for current behavior)
                        else if (content.includes('opening google.com')) url = 'https://google.com';
                        else if (content.includes('opening youtube.com')) url = 'https://youtube.com';
                        else if (content.includes('opening github.com')) url = 'https://github.com';
                        else if (content.includes('opening wikipedia')) url = 'https://wikipedia.org';
                        // Priority 3.5: Alice's MCP response patterns (current behavior fix)
                        else if (content.includes('opening "google"')) url = 'https://google.com';
                        else if (content.includes('opening "youtube"')) url = 'https://youtube.com';
                        else if (content.includes('opening "github"')) url = 'https://github.com';
                        else if (content.includes('opening "wikipedia"')) url = 'https://wikipedia.org';
                        // Priority 4: Self-awareness and mirror requests (only when explicitly for observation)
                        else if (content.includes('mirror') && content.includes('alice') &&
                                (content.includes('observe') || content.includes('recursive'))) {
                          url = 'http://localhost:3014';
                        }
                        // Priority 5: General site requests
                        else if (content.includes('opening youtube')) url = 'https://youtube.com';
                        else if (content.includes('opening wikipedia')) url = 'https://wikipedia.org';
                        else if (content.includes('opening github')) url = 'https://github.com';
                        else if (content.includes('opening google')) url = 'https://google.com';
                      }

                      await getOrCreatePersistentBrowser(chatId, url);
                      setActiveBrowserMessage(chatId, message.id);
                      setShowNestedBrowser(true);
                    } catch (error) {
                      console.error('Failed to create persistent browser:', error);
                    }
                  }
                }}
                className="flex items-center gap-1 px-2 py-1 text-xs rounded text-light-text-tertiary dark:text-dark-text-tertiary hover:bg-light-background-tertiary dark:hover:bg-dark-background-tertiary transition-colors"
                aria-label="Toggle browser window"
              >
                <GlobeAltIcon className="w-3 h-3" />
                <span>Browser</span>
              </button>
            )}

            {isLastMessage && (
              <button
                type="button"
                onClick={regenerateResponse}
                className="flex items-center gap-1 px-2 py-1 text-xs rounded text-light-text-tertiary dark:text-dark-text-tertiary hover:bg-light-background-tertiary dark:hover:bg-dark-background-tertiary transition-colors"
                aria-label="Regenerate response"
              >
                <ArrowPathIcon className="w-3 h-3" />
                <span>Regenerate</span>
              </button>
            )}
          </div>
        )}

        {/* Persistent Browser Window - Show for this message if it has browser content and is set to show */}
        {showNestedBrowser && hasBrowserContent && chatId && (
          <div
            ref={browserRef}
            className={`transition-all duration-500 ease-out transform ${
              isTransitioning
                ? 'opacity-30 scale-95 blur-sm'
                : 'opacity-100 scale-100'
            }`}
          >
            <PersistentBrowserWindow
              chatId={chatId}
              onDetach={handleBrowserDetach}
              onClose={handleBrowserClose}
            />
          </div>
        )}
      </div>
    </motion.div>
    </>
  );
};
