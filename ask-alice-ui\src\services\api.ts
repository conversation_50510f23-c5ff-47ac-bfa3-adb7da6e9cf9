import axios from 'axios';
import * as Sentry from '@sentry/react';

// Create axios instance with default config
const api = axios.create({
  baseURL: 'http://localhost:8003/api',
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 30000, // 30 seconds
});

// Request interceptor with Sentry integration
api.interceptors.request.use(
  (config) => {
    // Add Sentry trace headers for distributed tracing
    const transaction = Sentry.getCurrentHub().getScope()?.getTransaction();
    if (transaction) {
      const traceHeaders = Sentry.traceHeaders();
      Object.assign(config.headers, traceHeaders);
    }

    // Add request context to Sentry
    Sentry.addBreadcrumb({
      message: `API Request: ${config.method?.toUpperCase()} ${config.url}`,
      category: 'http',
      level: 'info',
      data: {
        url: config.url,
        method: config.method,
        baseURL: config.baseURL,
      },
    });

    // You can add auth token here if needed
    // const token = localStorage.getItem('token');
    // if (token) {
    //   config.headers.Authorization = `Bearer ${token}`;
    // }
    return config;
  },
  (error) => {
    // Capture request setup errors in Sentry
    Sentry.withScope((scope) => {
      scope.setTag('errorType', 'api_request_setup');
      scope.setLevel('error');
      scope.setContext('requestError', {
        message: error.message,
        config: error.config,
      });
      Sentry.captureException(error);
    });

    return Promise.reject(error);
  }
);

// Response interceptor with comprehensive Sentry integration
api.interceptors.response.use(
  (response) => {
    // Add successful response breadcrumb
    Sentry.addBreadcrumb({
      message: `API Response: ${response.status} ${response.config.method?.toUpperCase()} ${response.config.url}`,
      category: 'http',
      level: 'info',
      data: {
        status: response.status,
        statusText: response.statusText,
        url: response.config.url,
        method: response.config.method,
        responseTime: response.headers['x-response-time'],
      },
    });

    return response;
  },
  (error) => {
    // Enhanced error handling with Sentry integration
    console.error('API Error:', error);

    // Capture detailed error information in Sentry
    Sentry.withScope((scope) => {
      scope.setTag('errorType', 'api_response');
      scope.setLevel('error');

      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        scope.setTag('httpStatus', error.response.status);
        scope.setContext('apiError', {
          status: error.response.status,
          statusText: error.response.statusText,
          url: error.config?.url,
          method: error.config?.method,
          baseURL: error.config?.baseURL,
          responseData: error.response.data,
          headers: error.response.headers,
        });

        console.error('Response error:', error.response.data);

        // Handle specific error codes
        if (error.response.status === 401) {
          scope.setTag('errorSubtype', 'unauthorized');
          // Handle unauthorized
        } else if (error.response.status === 403) {
          scope.setTag('errorSubtype', 'forbidden');
        } else if (error.response.status === 404) {
          scope.setTag('errorSubtype', 'not_found');
        } else if (error.response.status >= 500) {
          scope.setTag('errorSubtype', 'server_error');
        }
      } else if (error.request) {
        // The request was made but no response was received
        scope.setTag('errorSubtype', 'network_error');
        scope.setContext('networkError', {
          url: error.config?.url,
          method: error.config?.method,
          timeout: error.config?.timeout,
          message: error.message,
        });
        console.error('Request error:', error.request);
      } else {
        // Something happened in setting up the request that triggered an Error
        scope.setTag('errorSubtype', 'request_setup');
        scope.setContext('setupError', {
          message: error.message,
          config: error.config,
        });
        console.error('Error:', error.message);
      }

      // Add breadcrumb for the error
      Sentry.addBreadcrumb({
        message: `API Error: ${error.message}`,
        category: 'http',
        level: 'error',
        data: {
          url: error.config?.url,
          method: error.config?.method,
          status: error.response?.status,
        },
      });

      Sentry.captureException(error);
    });

    return Promise.reject(error);
  }
);

// Chat API
export const chatApi = {
  sendMessage: (chatId: string, message: string, userId: string) =>
    api.post(`http://localhost:8003/api/chat/send`, { chatId, message, userId }),

  getChatHistory: (chatId: string) =>
    api.get(`/chat/history/${chatId}`),

  getChats: (userId: string) =>
    api.get('/chat/list', { params: { userId } }),

  createChat: (userId: string, title?: string) =>
    api.post('/chat/create', { userId, title }),

  deleteChat: (chatId: string, userId: string) =>
    api.delete(`/chat/${chatId}`, { params: { userId } }),
};

// Dashboard API
export const dashboardApi = {
  getMutationImpact: (timeRange?: string) =>
    api.get('/dashboard/mutation-impact', { params: { timeRange } }),

  getCognitiveStability: () =>
    api.get('/dashboard/cognitive-stability'),

  getEconomicHealth: (forecastPeriod?: number) =>
    api.get('/dashboard/economic-health', { params: { forecastPeriod } }),

  getSelfDreamForecast: () =>
    api.get('/dashboard/self-dream'),

  getViralNetwork: () =>
    api.get('/dashboard/viral-network'),

  getMemoryForest: () =>
    api.get('/dashboard/memory-forest'),

  getReflectionLog: (filter?: string, level?: string, source?: string) =>
    api.get('/dashboard/reflection-log', { params: { filter, level, source } }),
};

// System API
export const systemApi = {
  getSystemStatus: () =>
    api.get('/system/status'),

  getSystemComponents: () =>
    api.get('/system/components'),

  restartSystem: (component: string) =>
    api.post('/system/restart', { component }),

  getSystemLogs: (level?: string, component?: string, limit?: number) =>
    api.get('/system/logs', { params: { level, component, limit } }),
};

export default api;
