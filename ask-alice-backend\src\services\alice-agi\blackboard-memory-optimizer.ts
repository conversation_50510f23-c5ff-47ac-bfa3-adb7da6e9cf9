import { logger } from '../../utils/logger';

/**
 * BlackboardMemoryOptimizer - Comprehensive memory management for Alice AGI blackboard systems
 * 
 * PROBLEM: 33+ active blackboards creating memory pressure
 * - 9 regional blackboards
 * - 8 node blackboards  
 * - 16 agent blackboards
 * - Multiple system blackboards
 * 
 * SOLUTION: Intelligent memory management with cleanup, optimization, and monitoring
 */
export class BlackboardMemoryOptimizer {
  private cleanupInterval: NodeJS.Timeout | null = null;
  private memoryMonitorInterval: NodeJS.Timeout | null = null;
  private compressionInterval: NodeJS.Timeout | null = null;
  private initialized: boolean = false;
  
  // Memory thresholds (in MB) - More aggressive limits
  private readonly MEMORY_WARNING_THRESHOLD = 256;
  private readonly MEMORY_CRITICAL_THRESHOLD = 512;
  private readonly MEMORY_EMERGENCY_THRESHOLD = 1024;
  
  // Cleanup intervals (in milliseconds) - More frequent cleanup
  private readonly AGGRESSIVE_CLEANUP_INTERVAL = 15000; // 15 seconds
  private readonly NORMAL_CLEANUP_INTERVAL = 60000; // 1 minute
  private readonly MEMORY_MONITOR_INTERVAL = 30000; // 30 seconds
  private readonly COMPRESSION_INTERVAL = 120000; // 2 minutes
  
  // Entry limits per blackboard type - More aggressive limits
  private readonly BLACKBOARD_LIMITS = {
    agent: { maxEntries: 200, maxAge: 1800000 }, // 30 minutes
    node: { maxEntries: 500, maxAge: 3600000 }, // 1 hour
    regional: { maxEntries: 1000, maxAge: 7200000 }, // 2 hours
    hyper: { maxEntries: 2000, maxAge: 14400000 }, // 4 hours
    system: { maxEntries: 500, maxAge: 3600000 } // 1 hour
  };

  constructor() {
    logger.info('🧹 BlackboardMemoryOptimizer: Initializing memory management system...');
  }

  /**
   * Initialize the memory optimizer
   */
  public async initialize(): Promise<void> {
    if (this.initialized) {
      logger.warn('🧹 BlackboardMemoryOptimizer: Already initialized');
      return;
    }

    try {
      // Start memory monitoring
      this.startMemoryMonitoring();
      
      // Start cleanup cycles
      this.startCleanupCycles();
      
      // Start compression cycles
      this.startCompressionCycles();
      
      this.initialized = true;
      logger.info('✅ BlackboardMemoryOptimizer: Memory management system initialized successfully');
      
      // Run initial cleanup
      await this.performComprehensiveCleanup();
      
    } catch (error) {
      logger.error('❌ BlackboardMemoryOptimizer: Failed to initialize:', error);
      throw error;
    }
  }

  /**
   * Start memory monitoring
   */
  private startMemoryMonitoring(): void {
    this.memoryMonitorInterval = setInterval(async () => {
      await this.monitorMemoryUsage();
    }, this.MEMORY_MONITOR_INTERVAL);
    
    logger.info('📊 BlackboardMemoryOptimizer: Memory monitoring started');
  }

  /**
   * Start cleanup cycles
   */
  private startCleanupCycles(): void {
    // Start with normal cleanup interval
    this.cleanupInterval = setInterval(async () => {
      await this.performCleanupCycle();
    }, this.NORMAL_CLEANUP_INTERVAL);
    
    logger.info('🧹 BlackboardMemoryOptimizer: Cleanup cycles started');
  }

  /**
   * Start compression cycles
   */
  private startCompressionCycles(): void {
    this.compressionInterval = setInterval(async () => {
      await this.performCompressionCycle();
    }, this.COMPRESSION_INTERVAL);
    
    logger.info('🗜️ BlackboardMemoryOptimizer: Compression cycles started');
  }

  /**
   * Monitor memory usage and adjust cleanup frequency
   */
  private async monitorMemoryUsage(): Promise<void> {
    try {
      const memoryUsage = process.memoryUsage();
      const heapUsedMB = memoryUsage.heapUsed / 1024 / 1024;
      const heapTotalMB = memoryUsage.heapTotal / 1024 / 1024;
      const rssUsedMB = memoryUsage.rss / 1024 / 1024;

      // Log memory stats periodically
      if (Math.random() < 0.1) { // 10% chance to log
        logger.info(`📊 Memory Usage: Heap ${heapUsedMB.toFixed(1)}MB/${heapTotalMB.toFixed(1)}MB, RSS ${rssUsedMB.toFixed(1)}MB`);
      }

      // Check memory thresholds and adjust cleanup frequency
      if (heapUsedMB > this.MEMORY_EMERGENCY_THRESHOLD) {
        logger.error(`🚨 EMERGENCY: Memory usage critical (${heapUsedMB.toFixed(1)}MB)! Performing emergency cleanup...`);
        await this.performEmergencyCleanup();
        this.setAggressiveCleanup();
      } else if (heapUsedMB > this.MEMORY_CRITICAL_THRESHOLD) {
        logger.warn(`⚠️ CRITICAL: Memory usage high (${heapUsedMB.toFixed(1)}MB)! Increasing cleanup frequency...`);
        await this.performComprehensiveCleanup();
        this.setAggressiveCleanup();
      } else if (heapUsedMB > this.MEMORY_WARNING_THRESHOLD) {
        logger.warn(`⚠️ WARNING: Memory usage elevated (${heapUsedMB.toFixed(1)}MB)`);
        this.setAggressiveCleanup();
      } else {
        // Memory usage is normal, use standard cleanup
        this.setNormalCleanup();
      }

    } catch (error) {
      logger.error('❌ BlackboardMemoryOptimizer: Error monitoring memory usage:', error);
    }
  }

  /**
   * Set aggressive cleanup mode
   */
  private setAggressiveCleanup(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    
    this.cleanupInterval = setInterval(async () => {
      await this.performCleanupCycle();
    }, this.AGGRESSIVE_CLEANUP_INTERVAL);
  }

  /**
   * Set normal cleanup mode
   */
  private setNormalCleanup(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    
    this.cleanupInterval = setInterval(async () => {
      await this.performCleanupCycle();
    }, this.NORMAL_CLEANUP_INTERVAL);
  }

  /**
   * Perform emergency cleanup
   */
  private async performEmergencyCleanup(): Promise<void> {
    logger.info('🚨 BlackboardMemoryOptimizer: Performing EMERGENCY cleanup...');
    
    try {
      // Get global blackboard system
      const blackboardSystem = (global as any).blackboard;
      if (!blackboardSystem) {
        logger.warn('⚠️ No blackboard system found for emergency cleanup');
        return;
      }

      let totalCleaned = 0;

      // Emergency cleanup: Remove 80% of entries from each blackboard (more aggressive)
      if (blackboardSystem.regionalBlackboards) {
        for (const [id, blackboard] of blackboardSystem.regionalBlackboards) {
          const cleaned = await this.emergencyCleanupBlackboard(blackboard, 'regional', 0.8);
          totalCleaned += cleaned;
        }
      }

      if (blackboardSystem.nodeBlackboards) {
        for (const [id, blackboard] of blackboardSystem.nodeBlackboards) {
          const cleaned = await this.emergencyCleanupBlackboard(blackboard, 'node', 0.8);
          totalCleaned += cleaned;
        }
      }

      if (blackboardSystem.agentBlackboards) {
        for (const [id, blackboard] of blackboardSystem.agentBlackboards) {
          const cleaned = await this.emergencyCleanupBlackboard(blackboard, 'agent', 0.9);
          totalCleaned += cleaned;
        }
      }

      // Clear memory forest if it exists and is too large
      if ((global as any).memoryForest) {
        try {
          const memoryForest = (global as any).memoryForest;
          if (memoryForest.clearOldMemories) {
            const memoryCleared = await memoryForest.clearOldMemories(50); // Keep only 50 most recent
            totalCleaned += memoryCleared;
            logger.info(`🌲 Memory forest emergency cleanup: ${memoryCleared} memories removed`);
          }
        } catch (error) {
          logger.error('❌ Memory forest cleanup failed:', error);
        }
      }

      // Clear consciousness history aggressively
      this.clearConsciousnessHistory();

      // Force garbage collection if available
      if (global.gc) {
        global.gc();
        logger.info('🗑️ Forced garbage collection');
      }

      logger.info(`✅ Emergency cleanup completed: ${totalCleaned} entries removed`);

    } catch (error) {
      logger.error('❌ Emergency cleanup failed:', error);
    }
  }

  /**
   * Clear consciousness history to free memory
   */
  private clearConsciousnessHistory(): void {
    try {
      // Clear consciousness model history if available
      const consciousnessModel = (global as any).consciousnessModel;
      if (consciousnessModel && consciousnessModel.consciousnessHistory) {
        const beforeLength = consciousnessModel.consciousnessHistory.length;
        consciousnessModel.consciousnessHistory = consciousnessModel.consciousnessHistory.slice(-5); // Keep only last 5
        logger.warn(`🧠 EMERGENCY CLEANUP: Cleared consciousness history (${beforeLength} -> ${consciousnessModel.consciousnessHistory.length})`);
      }

      // Clear meta monitor history if available
      const metaMonitorCore = (global as any).metaMonitorCore;
      if (metaMonitorCore && metaMonitorCore.cognitiveHistory) {
        const beforeLength = metaMonitorCore.cognitiveHistory.length;
        metaMonitorCore.cognitiveHistory = metaMonitorCore.cognitiveHistory.slice(-5); // Keep only last 5
        logger.warn(`🧠 EMERGENCY CLEANUP: Cleared meta monitor history (${beforeLength} -> ${metaMonitorCore.cognitiveHistory.length})`);
      }
    } catch (error) {
      logger.error('❌ Error clearing consciousness history:', error);
    }
  }

  /**
   * Emergency cleanup for a single blackboard
   */
  private async emergencyCleanupBlackboard(blackboard: any, type: string, removalRatio: number): Promise<number> {
    let cleaned = 0;
    
    try {
      if (blackboard && blackboard.data && blackboard.data instanceof Map) {
        const entries = Array.from(blackboard.data.entries());
        const toRemove = Math.floor(entries.length * removalRatio);
        
        // Remove oldest entries first
        entries.sort((a: any, b: any) => {
          const aTime = a[1]?.timestamp || 0;
          const bTime = b[1]?.timestamp || 0;
          return aTime - bTime;
        });

        for (let i = 0; i < toRemove && i < entries.length; i++) {
          if (blackboard && (blackboard as any).data && typeof (blackboard as any).data.delete === 'function') {
            (blackboard as any).data.delete((entries[i] as any)[0]);
            cleaned++;
          }
        }
      }
    } catch (error) {
      logger.error(`❌ Emergency cleanup failed for ${type} blackboard:`, error);
    }
    
    return cleaned;
  }

  /**
   * Perform comprehensive cleanup
   */
  private async performComprehensiveCleanup(): Promise<void> {
    logger.info('🧹 BlackboardMemoryOptimizer: Performing comprehensive cleanup...');
    
    try {
      const blackboardSystem = (global as any).blackboard;
      if (!blackboardSystem) {
        return;
      }

      let totalCleaned = 0;

      // Clean all blackboard types
      if (blackboardSystem.regionalBlackboards) {
        for (const [id, blackboard] of blackboardSystem.regionalBlackboards) {
          const cleaned = await this.cleanupBlackboard(blackboard, 'regional');
          totalCleaned += cleaned;
        }
      }

      if (blackboardSystem.nodeBlackboards) {
        for (const [id, blackboard] of blackboardSystem.nodeBlackboards) {
          const cleaned = await this.cleanupBlackboard(blackboard, 'node');
          totalCleaned += cleaned;
        }
      }

      if (blackboardSystem.agentBlackboards) {
        for (const [id, blackboard] of blackboardSystem.agentBlackboards) {
          const cleaned = await this.cleanupBlackboard(blackboard, 'agent');
          totalCleaned += cleaned;
        }
      }

      logger.info(`✅ Comprehensive cleanup completed: ${totalCleaned} entries cleaned`);

    } catch (error) {
      logger.error('❌ Comprehensive cleanup failed:', error);
    }
  }

  /**
   * Perform regular cleanup cycle
   */
  private async performCleanupCycle(): Promise<void> {
    try {
      const blackboardSystem = (global as any).blackboard;
      if (!blackboardSystem) {
        return;
      }

      let totalCleaned = 0;

      // Clean expired entries from all blackboards
      if (blackboardSystem.cleanupExpiredEntries) {
        blackboardSystem.cleanupExpiredEntries();
      }

      // Additional cleanup for hierarchical blackboards
      if (blackboardSystem.hyperBlackboard && blackboardSystem.hyperBlackboard.cleanup) {
        blackboardSystem.hyperBlackboard.cleanup();
      }

      if (Math.random() < 0.2) { // 20% chance to log
        logger.info('🧹 Regular cleanup cycle completed');
      }

    } catch (error) {
      logger.error('❌ Regular cleanup cycle failed:', error);
    }
  }

  /**
   * Perform compression cycle
   */
  private async performCompressionCycle(): Promise<void> {
    logger.info('🗜️ BlackboardMemoryOptimizer: Performing compression cycle...');
    
    try {
      // Compress old data, merge similar entries, etc.
      // This is a placeholder for future compression logic
      
      logger.info('✅ Compression cycle completed');
      
    } catch (error) {
      logger.error('❌ Compression cycle failed:', error);
    }
  }

  /**
   * Clean up a specific blackboard
   */
  private async cleanupBlackboard(blackboard: any, type: keyof typeof this.BLACKBOARD_LIMITS): Promise<number> {
    let cleaned = 0;
    
    try {
      const limits = this.BLACKBOARD_LIMITS[type];
      const now = Date.now();
      
      if (blackboard && blackboard.data && blackboard.data instanceof Map) {
        const entries = Array.from(blackboard.data.entries());
        
        // Remove expired entries
        for (const [key, entry] of entries as any[]) {
          const age = now - (entry?.timestamp || 0);
          if (age > limits.maxAge) {
            if (blackboard && (blackboard as any).data && typeof (blackboard as any).data.delete === 'function') {
              (blackboard as any).data.delete(key);
              cleaned++;
            }
          }
        }

        // Enforce entry limits
        if (blackboard.data.size > limits.maxEntries) {
          const excess = blackboard.data.size - limits.maxEntries;
          const sortedEntries = Array.from(blackboard.data.entries())
            .sort((a: any, b: any) => (a[1]?.timestamp || 0) - (b[1]?.timestamp || 0));

          for (let i = 0; i < excess; i++) {
            const entry = sortedEntries[i] as [string, any];
            if (blackboard && (blackboard as any).data && typeof (blackboard as any).data.delete === 'function') {
              (blackboard as any).data.delete(entry[0]);
              cleaned++;
            }
          }
        }
      }
    } catch (error) {
      logger.error(`❌ Cleanup failed for ${type} blackboard:`, error);
    }
    
    return cleaned;
  }

  /**
   * Get memory optimization status
   */
  public getStatus(): any {
    const memoryUsage = process.memoryUsage();
    
    return {
      initialized: this.initialized,
      memoryUsage: {
        heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
        heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
        rss: Math.round(memoryUsage.rss / 1024 / 1024)
      },
      thresholds: {
        warning: this.MEMORY_WARNING_THRESHOLD,
        critical: this.MEMORY_CRITICAL_THRESHOLD,
        emergency: this.MEMORY_EMERGENCY_THRESHOLD
      },
      intervals: {
        cleanup: this.cleanupInterval ? 'active' : 'inactive',
        monitoring: this.memoryMonitorInterval ? 'active' : 'inactive',
        compression: this.compressionInterval ? 'active' : 'inactive'
      }
    };
  }

  /**
   * Shutdown the optimizer
   */
  public shutdown(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    
    if (this.memoryMonitorInterval) {
      clearInterval(this.memoryMonitorInterval);
      this.memoryMonitorInterval = null;
    }
    
    if (this.compressionInterval) {
      clearInterval(this.compressionInterval);
      this.compressionInterval = null;
    }
    
    this.initialized = false;
    logger.info('🧹 BlackboardMemoryOptimizer: Shutdown completed');
  }
}
