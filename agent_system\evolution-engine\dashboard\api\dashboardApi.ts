﻿import { DashboardConfig, AgentMetrics } from '../types';
import { MetricsAPI } from '../../metrics/api';
import { AgentType } from '../../../../../agents/agent-base/types';

// Create a singleton instance of the MetricsAPI
const metricsAPI = new MetricsAPI();

/**
 * Fetch the dashboard configuration
 */
export async function fetchDashboardConfig(): Promise<DashboardConfig> {
  try {
    // In a real implementation, this would fetch from an API
    // For now, we'll import the local config file
    const response = await fetch('/agent_system/evolution-engine/dashboard/config.json');
    if (!response.ok) {
      throw new Error(`Failed to fetch dashboard config: ${response.statusText}`);
    }
    return await response.json();
  } catch (error: unknown) {
    console.error('Error fetching dashboard config:', error);
    // Return a minimal default config if the fetch fails
    return {
      dashboard: {
        title: 'Evolution System Dashboard',
        description: 'Monitoring agent performance and evolution',
        refreshInterval: 60000,
        layout: {
          type: 'grid',
          columns: 3
        }
      },
      panels: [],
      agentGroups: [],
      metricThresholds: {
        overall_performance: {
          warning: 0.7,
          critical: 0.5
        }
      },
      alerts: {
        enabled: true,
        channels: [],
        rules: []
      }
    };
  }
}

/**
 * Fetch agent metrics data
 */
export async function fetchAgentMetrics(): Promise<AgentMetrics> {
  try {
    // Get aggregated metrics from the MetricsAPI
    const timeRange = {
      start: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
      end: new Date()
    };

    const aggregatedMetrics = metricsAPI.getAggregatedMetrics(timeRange);

    // If we have real metrics, use them
    if (Object.keys(aggregatedMetrics.agentSummaries).length > 0) {
      return convertAggregatedMetricsToAgentMetrics(aggregatedMetrics);
    }

    // Otherwise, generate mock data
    return generateMockMetrics();
  } catch (error: unknown) {
    console.error('Error fetching agent metrics:', error);
    // Fall back to mock data
    return generateMockMetrics();
  }
}

/**
 * Convert aggregated metrics to the format expected by the dashboard
 */
function convertAggregatedMetricsToAgentMetrics(aggregatedMetrics: any): AgentMetrics {
  const { agentSummaries } = aggregatedMetrics;

  // Count agents by status
  let agentsBelowThreshold = 0;
  let agentsEvolving = 0;
  let successfulEvolutions = 0;
  let failedEvolutions = 0;

  // Convert agent summaries to the format expected by the dashboard
  const agents: Record<string, any> = {};

  Object.entries(agentSummaries).forEach(([agentId, summary]: [string, any]) => {
    const { agentType, averagePerformance, latestMetrics, evolutionStatus, lastUpdated } = summary;

    // Track system metrics
    if (averagePerformance < 0.7) agentsBelowThreshold++;
    if (evolutionStatus === 'in_progress') agentsEvolving++;
    if (evolutionStatus === 'successful') successfulEvolutions++;
    if (evolutionStatus === 'failed') failedEvolutions++;

    // Convert to dashboard format
    agents[agentId] = {
      agent_id: agentId,
      agent_type: agentType,
      overall_performance: averagePerformance || 0.5,
      last_evolution: lastUpdated ? new Date(lastUpdated).toISOString() : null,
      evolution_status: evolutionStatus,
      metrics: {
        success_rate: latestMetrics.success_rate || 0.7,
        response_time: latestMetrics.response_time || 2000,
        accuracy: latestMetrics.accuracy || 0.7,
        efficiency: latestMetrics.efficiency || 0.6
      }
    };

    // Add evolution metrics if available
    if (latestMetrics.performance_before_evolution && latestMetrics.performance_after_evolution) {
      agents[agentId].evolution_metrics = {
        performance_before_evolution: latestMetrics.performance_before_evolution,
        performance_after_evolution: latestMetrics.performance_after_evolution
      };
    }
  });

  // Get evolution events
  const evolutionImpact = metricsAPI.getEvolutionImpact();
  const evolutionEvents: any[] = [];

  Object.entries(evolutionImpact).forEach(([key, impact]: [string, any]) => {
    const [agentId, evolutionId] = key.split('-');
    const agentType = agents[agentId]?.agent_type || 'Unknown';

    // Add 'completed' event
    evolutionEvents.push({
      id: `evolution-${agentId}-completed`,
      agent_id: agentId,
      timestamp: new Date().toISOString(), // We don't have the actual timestamp
      type: 'completed',
      details: `Evolution completed successfully for ${agentType}`,
      performance_before: impact.before,
      performance_after: impact.after
    });
  });

  return {
    timestamp: new Date().toISOString(),
    system: {
      total_agents: Object.keys(agents).length,
      agents_below_threshold: agentsBelowThreshold,
      agents_evolving: agentsEvolving,
      successful_evolutions: successfulEvolutions,
      failed_evolutions: failedEvolutions
    },
    agents,
    evolution_events: evolutionEvents
  };
}

/**
 * Generate mock metrics data for development and testing
 */
function generateMockMetrics(): AgentMetrics {
  const agentTypes = [
    'EnhancedManagerAgent',
    'EnhancedSourcingAgent',
    'EnhancedBuyingAgent',
    'EnhancedListingAgent',
    'EnhancedShippingAgent',
    'EnhancedMarketAnalysisAgent',
    'EnhancedPricingAgent',
    'EnhancedReinforcementLearningAgent',
    'EnhancedErrorHandlingAgent',
    'EnhancedFeedbackLoopAgent',
    'EnhancedPlatformAdaptationAgent',
    'EnhancedComplianceAgent'
  ];

  const agents: Record<string, any> = {};
  let agentsBelowThreshold = 0;
  let agentsEvolving = 0;
  let successfulEvolutions = 0;
  let failedEvolutions = 0;
  const evolutionEvents: any[] = [];

  // Generate data for each agent
  agentTypes.forEach((agentType, index) => {
    const agentId = `agent-${index + 1}`;
    const overallPerformance = Math.random() * 0.4 + 0.6; // 0.6 to 1.0
    const evolutionStatus = Math.random() > 0.8 ?
      (Math.random() > 0.7 ? 'failed' : 'in_progress') :
      (Math.random() > 0.5 ? 'successful' : 'none');

    // Track system metrics
    if (overallPerformance < 0.7) agentsBelowThreshold++;
    if (evolutionStatus === 'in_progress') agentsEvolving++;
    if (evolutionStatus === 'successful') successfulEvolutions++;
    if (evolutionStatus === 'failed') failedEvolutions++;

    // Generate last evolution date (within the last 30 days)
    const lastEvolution = evolutionStatus !== 'none' ?
      new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString() :
      null;

    // Generate agent metrics
    agents[agentId] = {
      agent_id: agentId,
      agent_type: agentType,
      overall_performance: overallPerformance,
      last_evolution: lastEvolution,
      evolution_status: evolutionStatus,
      evolution_failure_reason: evolutionStatus === 'failed' ?
        'Failed to improve performance metrics' :
        undefined,
      metrics: {
        success_rate: Math.random() * 0.3 + 0.7, // 0.7 to 1.0
        response_time: Math.random() * 4000 + 1000, // 1000 to 5000 ms
        accuracy: Math.random() * 0.3 + 0.7, // 0.7 to 1.0
        efficiency: Math.random() * 0.4 + 0.6 // 0.6 to 1.0
      }
    };

    // Add evolution metrics if the agent has evolved
    if (evolutionStatus === 'successful' || evolutionStatus === 'failed') {
      const performanceBefore = Math.random() * 0.3 + 0.4; // 0.4 to 0.7
      const performanceAfter = evolutionStatus === 'successful' ?
        Math.random() * 0.3 + 0.7 : // 0.7 to 1.0 for successful
        Math.random() * 0.2 + 0.4; // 0.4 to 0.6 for failed

      agents[agentId].evolution_metrics = {
        performance_before_evolution: performanceBefore,
        performance_after_evolution: performanceAfter
      };

      // Add evolution events
      const eventTimestamp = new Date(lastEvolution || Date.now()).toISOString();

      // Add 'started' event
      evolutionEvents.push({
        id: `evolution-${agentId}-started`,
        agent_id: agentId,
        timestamp: new Date(new Date(eventTimestamp).getTime() - 3600000).toISOString(), // 1 hour before completion
        type: 'started',
        details: `Evolution started for ${agentType}`,
        performance_before: performanceBefore
      });

      // Add 'completed' or 'failed' event
      evolutionEvents.push({
        id: `evolution-${agentId}-${evolutionStatus === 'successful' ? 'completed' : 'failed'}`,
        agent_id: agentId,
        timestamp: eventTimestamp,
        type: evolutionStatus === 'successful' ? 'completed' : 'failed',
        details: evolutionStatus === 'successful' ?
          `Evolution completed successfully for ${agentType}` :
          `Evolution failed for ${agentType}: Failed to improve performance metrics`,
        performance_before: performanceBefore,
        performance_after: performanceAfter
      });
    }

    // Record mock metrics in the metrics API for future use
    recordMockMetricsForAgent(agentId, agentType as AgentType, agents[agentId]);
  });

  // Sort evolution events by timestamp
  evolutionEvents.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());

  return {
    timestamp: new Date().toISOString(),
    system: {
      total_agents: agentTypes.length,
      agents_below_threshold: agentsBelowThreshold,
      agents_evolving: agentsEvolving,
      successful_evolutions: successfulEvolutions,
      failed_evolutions: failedEvolutions
    },
    agents,
    evolution_events: evolutionEvents
  };
}

/**
 * Record mock metrics for an agent in the metrics API
 */
function recordMockMetricsForAgent(agentId: string, agentType: AgentType, agentData: any): void {
  // Record overall performance
  metricsAPI.recordMetric(agentId, agentType, {
    name: 'overall_performance',
    value: agentData.overall_performance,
    description: 'Overall agent performance',
    metricType: 'ratio',
    timestamp: new Date()
  });

  // Record individual metrics
  Object.entries(agentData.metrics).forEach(([metricName, value]: [string, any]) => {
    metricsAPI.recordMetric(agentId, agentType, {
      name: metricName,
      value,
      description: `${metricName.replace('_', ' ')}`,
      metricType: metricName === 'response_time' ? 'time' : 'ratio',
      timestamp: new Date()
    });
  });

  // Record evolution status if applicable
  if (agentData.evolution_status !== 'none') {
    metricsAPI.recordMetric(agentId, agentType, {
      name: 'evolution_status',
      value: agentData.evolution_status === 'successful' ? 1 :
             agentData.evolution_status === 'in_progress' ? 0.5 : 0,
      description: 'Evolution status',
      metricType: 'score',
      timestamp: new Date(),
      additionalData: {
        status: agentData.evolution_status,
        evolutionId: `evolution-${agentId}-${Date.now()}`
      }
    });

    // Record evolution metrics if available
    if (agentData.evolution_metrics) {
      metricsAPI.recordMetric(agentId, agentType, {
        name: 'performance_before_evolution',
        value: agentData.evolution_metrics.performance_before_evolution,
        description: 'Performance before evolution',
        metricType: 'ratio',
        timestamp: new Date()
      });

      metricsAPI.recordMetric(agentId, agentType, {
        name: 'performance_after_evolution',
        value: agentData.evolution_metrics.performance_after_evolution,
        description: 'Performance after evolution',
        metricType: 'ratio',
        timestamp: new Date()
      });
    }
  }
}

